# /opt/KylinRobot-v2 普通用户访问修复说明

## 问题描述

使用 `bash build_x86-deb-optimized-remote-display-fixed.sh --arch x64` 脚本生成的DEB包在安装后，`/opt/KylinRobot-v2` 目录的所有者是 `root`，普通用户无法访问。

## 解决方案

修改了构建脚本中的权限设置逻辑，实现智能的用户权限检测和设置。

### 修改内容

1. **智能用户检测**：
   - 检测 `$SUDO_USER` 环境变量（当用户使用sudo安装时）
   - 如果检测失败，回退到 `$USER` 变量
   - 如果都无法检测到普通用户，使用通用权限设置

2. **权限设置策略**：
   - **优先方案**：设置为安装用户所有，其他用户可读可执行
   - **备用方案**：设置为root所有，但所有用户可读可执行

3. **具体权限**：
   - 目录：755 (所有者可读写执行，其他用户可读执行)
   - 普通文件：644 (所有者可读写，其他用户可读)
   - 可执行文件(.py, .sh)：755 (所有用户可执行)

### 代码逻辑

```bash
# 检测当前安装用户（如果是通过sudo安装）
INSTALL_USER="${SUDO_USER:-$USER}"
if [ "$INSTALL_USER" = "root" ] || [ -z "$INSTALL_USER" ]; then
    # 备用方案：通用权限设置
    chown -R root:root /opt/KylinRobot-v2
    # 设置所有用户可读可执行权限
else
    # 优先方案：设置为安装用户所有
    INSTALL_GROUP=$(id -gn "$INSTALL_USER" 2>/dev/null || echo "users")
    chown -R "$INSTALL_USER:$INSTALL_GROUP" /opt/KylinRobot-v2
    # 设置权限让其他用户也可以访问
fi
```

## 使用方法

1. **构建DEB包**：
   ```bash
   bash build_x86-deb-optimized-remote-display-fixed.sh --arch x64
   ```

2. **安装DEB包**：
   ```bash
   sudo dpkg -i dist/kylinrobot-ide_*.deb
   ```

3. **验证权限**：
   ```bash
   ls -la /opt/KylinRobot-v2
   ```

## 预期结果

### 场景1：通过sudo安装（推荐）
```bash
sudo dpkg -i kylinrobot-ide_*.deb
```
- `/opt/KylinRobot-v2` 所有者：执行sudo的普通用户
- 权限：用户可读写执行，其他用户可读执行
- 示例：`drwxr-xr-x kylin kylin /opt/KylinRobot-v2`

### 场景2：直接以root安装
```bash
# 切换到root用户后安装
dpkg -i kylinrobot-ide_*.deb
```
- `/opt/KylinRobot-v2` 所有者：root
- 权限：所有用户可读执行
- 示例：`drwxr-xr-x root root /opt/KylinRobot-v2`

## 兼容性

- ✅ 支持所有Linux发行版
- ✅ 兼容不同的桌面环境
- ✅ 支持多用户系统
- ✅ 向后兼容原有安装方式

## 测试验证

运行测试脚本验证修改：
```bash
bash test_user_permissions.sh
```

## 修改文件

- `build_x86-deb-optimized-remote-display-fixed.sh`：主要构建脚本
- `test_user_permissions.sh`：测试验证脚本

## 注意事项

1. **权限继承**：新创建的文件会继承目录的权限设置
2. **安全性**：保持了系统安全性，只是让普通用户可以读取和执行
3. **兼容性**：不影响原有的功能，只是改善了用户体验
4. **回退机制**：如果用户检测失败，自动使用安全的备用方案

## 相关问题

如果安装后仍然无法访问，可以手动修复权限：
```bash
# 方法1：设置为当前用户所有
sudo chown -R $USER:$USER /opt/KylinRobot-v2

# 方法2：设置通用访问权限
sudo chmod -R 755 /opt/KylinRobot-v2
```
