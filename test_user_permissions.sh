#!/bin/bash
# 测试脚本：验证修改后的构建脚本是否正确设置普通用户权限

echo "=== 测试修改后的构建脚本权限设置 ==="
echo ""

# 检查构建脚本是否存在
BUILD_SCRIPT="build_x86-deb-optimized-remote-display-fixed.sh"
if [ ! -f "$BUILD_SCRIPT" ]; then
    echo "❌ 错误: 构建脚本不存在: $BUILD_SCRIPT"
    exit 1
fi

echo "✅ 构建脚本存在: $BUILD_SCRIPT"
echo ""

# 检查脚本中的权限设置逻辑
echo "🔍 检查脚本中的权限设置逻辑..."

# 检查是否包含SUDO_USER检测
if grep -q "SUDO_USER" "$BUILD_SCRIPT"; then
    echo "  ✅ 包含SUDO_USER检测逻辑"
else
    echo "  ❌ 缺少SUDO_USER检测逻辑"
fi

# 检查是否包含普通用户权限设置
if grep -q "设置为用户.*所有" "$BUILD_SCRIPT"; then
    echo "  ✅ 包含普通用户权限设置逻辑"
else
    echo "  ❌ 缺少普通用户权限设置逻辑"
fi

# 检查是否包含备用权限设置
if grep -q "所有用户可读可执行" "$BUILD_SCRIPT"; then
    echo "  ✅ 包含备用权限设置逻辑"
else
    echo "  ❌ 缺少备用权限设置逻辑"
fi

echo ""

# 显示关键的权限设置代码段
echo "📋 关键权限设置代码段:"
echo "----------------------------------------"
grep -A 20 "设置正确的权限（支持普通用户访问）" "$BUILD_SCRIPT" | head -20
echo "----------------------------------------"
echo ""

# 检查脚本语法
echo "🔧 检查脚本语法..."
if bash -n "$BUILD_SCRIPT"; then
    echo "  ✅ 脚本语法正确"
else
    echo "  ❌ 脚本语法错误"
    exit 1
fi

echo ""

# 显示修改摘要
echo "📊 修改摘要:"
echo "  🎯 目标: 让/opt/KylinRobot-v2目录支持普通用户访问"
echo "  🔧 方案: 智能检测安装用户，优先设置为普通用户所有"
echo "  🛡️ 备用: 如果检测失败，使用通用权限设置"
echo "  📁 权限: 目录755，文件644，可执行文件755"
echo "  👥 访问: 所有用户可读可执行，所有者可写"
echo ""

echo "✅ 测试完成！构建脚本已修改为支持普通用户访问/opt/KylinRobot-v2"
echo ""
echo "🚀 使用方法:"
echo "  1. 运行构建脚本: bash $BUILD_SCRIPT --arch x64"
echo "  2. 安装生成的DEB包: sudo dpkg -i dist/kylinrobot-ide_*.deb"
echo "  3. 验证权限: ls -la /opt/KylinRobot-v2"
echo ""
echo "🔍 预期结果:"
echo "  - 如果通过sudo安装，/opt/KylinRobot-v2 应该属于执行sudo的普通用户"
echo "  - 如果直接以root安装，/opt/KylinRobot-v2 属于root但所有用户可访问"
echo "  - 所有用户都可以读取和执行其中的文件"
