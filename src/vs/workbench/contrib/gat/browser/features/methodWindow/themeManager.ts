/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2023-2024 Your Name. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../../../base/common/lifecycle.js';
import { IThemeService } from '../../../../../../platform/theme/common/themeService.js';
import { ColorScheme } from '../../../../../../platform/theme/common/theme.js';
import { ILogService } from '../../../../../../platform/log/common/log.js';
import { TITLE_BAR_ACTIVE_BACKGROUND, TITLE_BAR_ACTIVE_FOREGROUND } from '../../../../../common/theme.js';

/**
 * 负责处理窗口的主题相关功能
 */
export class ThemeManager extends Disposable {
    private isDarkTheme: boolean = false;
    private isHighContrastTheme: boolean = false;
    private forceLightTheme: boolean = true; // 强制使用浅色主题

    constructor(
        @IThemeService private readonly themeService: IThemeService,
        @ILogService private readonly logService: ILogService
    ) {
        super();

        // 获取当前主题信息
        this.updateThemeInfo();

        // 监听主题变化
        this._register(this.themeService.onDidColorThemeChange(() => {
            this.updateThemeInfo();
            this.logService.info('主题已更新');
        }));
    }

    /**
     * 更新主题信息
     */
    private updateThemeInfo(): void {
        const currentTheme = this.themeService.getColorTheme();
        // 如果强制浅色主题，则始终使用浅色主题
        if (this.forceLightTheme) {
            this.isDarkTheme = false;
        } else {
            this.isDarkTheme = currentTheme.type === ColorScheme.DARK || currentTheme.type === ColorScheme.HIGH_CONTRAST_DARK;
        }
        this.isHighContrastTheme = currentTheme.type === ColorScheme.HIGH_CONTRAST_DARK || currentTheme.type === ColorScheme.HIGH_CONTRAST_LIGHT;
    }

    /**
     * 应用主题到窗口
     */
    public applyThemeToWindow(doc: Document): void {
        const body = doc.body;
        if (!body) {
            return;
        }

        // 移除所有主题类
        body.classList.remove('vscode-light', 'vscode-dark', 'vscode-high-contrast', 'vscode-high-contrast-light');

        // 添加当前主题类
        if (this.forceLightTheme) {
            // 强制使用浅色主题
            body.classList.add('vscode-light');

            // 如果是高对比度模式，使用浅色高对比度
            if (this.isHighContrastTheme) {
                body.classList.add('vscode-high-contrast');
                body.classList.add('vscode-high-contrast-light');
            }
        } else {
            // 正常使用当前主题
            if (this.isDarkTheme) {
                body.classList.add('vscode-dark');
            } else {
                body.classList.add('vscode-light');
            }

            if (this.isHighContrastTheme) {
                body.classList.add('vscode-high-contrast');
                if (!this.isDarkTheme) {
                    body.classList.add('vscode-high-contrast-light');
                }
            }
        }
    }

    /**
     * 获取当前是否为深色主题
     */
    public isDark(): boolean {
        // 如果强制浅色主题，始终返回 false
        if (this.forceLightTheme) {
            return false;
        }
        return this.isDarkTheme;
    }

    /**
     * 获取当前是否为高对比度主题
     */
    public isHighContrast(): boolean {
        return this.isHighContrastTheme;
    }

    /**
     * 获取窗口样式
     */
    public getStyles(): string {
        const currentTheme = this.themeService.getColorTheme();
        const titleBarBackground = currentTheme.getColor(TITLE_BAR_ACTIVE_BACKGROUND)?.toString() || '#dddddd';
        const titleBarForeground = currentTheme.getColor(TITLE_BAR_ACTIVE_FOREGROUND)?.toString() || '#333333';

        // 定义浅色主题的 CSS 变量
        const lightThemeVars = `
            :root {
                --container-padding: 20px;
                --input-padding-vertical: 6px;
                --input-padding-horizontal: 8px;
                --input-margin-vertical: 4px;
                --input-margin-horizontal: 0;
                --border-color: #d4d4d4;
                --background-color: #ffffff;
                --foreground-color: #333333;
                --header-background: ${titleBarBackground};
                --header-foreground: ${titleBarForeground};
                --highlight-color: #007fd4;
                --input-background: #ffffff;
                --input-foreground: #333333;
                --button-background: #0e639c;
                --button-foreground: #ffffff;
                --button-hover-background: #1177bb;
                --sidebar-background: #f3f3f3;
                --sidebar-foreground: #333333;
                --description-background: #f5f5f5;
                --description-foreground: #333333;
            }`;

        // 定义深色主题的 CSS 变量
        const darkThemeVars = `
            :root {
                --container-padding: 20px;
                --input-padding-vertical: 6px;
                --input-padding-horizontal: 8px;
                --input-margin-vertical: 4px;
                --input-margin-horizontal: 0;
                --border-color: var(--vscode-panel-border, #3c3c3c);
                --background-color: var(--vscode-editor-background, #1e1e1e);
                --header-background: ${titleBarBackground};
                --header-foreground: ${titleBarForeground};
                --foreground-color: var(--vscode-editor-foreground, #cccccc);
                --highlight-color: var(--vscode-focusBorder, #007fd4);
                --input-background: var(--vscode-input-background, #3c3c3c);
                --input-foreground: var(--vscode-input-foreground, #cccccc);
                --button-background: var(--vscode-button-background, #0e639c);
                --button-foreground: var(--vscode-button-foreground, #ffffff);
                --button-hover-background: var(--vscode-button-hoverBackground, #1177bb);
                --sidebar-background: var(--vscode-sideBar-background, #252526);
                --sidebar-foreground: var(--vscode-sideBar-foreground, #cccccc);
                --description-background: var(--vscode-textBlockQuote-background, #0e639c29);
                --description-foreground: var(--vscode-textBlockQuote-foreground, #cccccc);
            }`;

        // 共用的 CSS 样式
        const commonStyles = `
            html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
                overflow: hidden;
                background-color: var(--background-color);
                color: var(--foreground-color);
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                font-size: 13px;
                line-height: 1.4;
            }

            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 20px;
                background-color: var(--header-background);
                border-bottom: 1px solid var(--border-color);
                cursor: move; /* 添加移动光标样式 */
            }

            .header-title {
                font-size: 16px;
                font-weight: bold;
            }

            .header-actions {
                display: flex;
                gap: 10px;
            }

            .container {
                display: flex;
                height: calc(100% - 50px);
                overflow: hidden;
            }

            .sidebar {
                width: 250px;
                border-right: 1px solid var(--border-color);
                background-color: var(--sidebar-background);
                color: var(--sidebar-foreground);
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            .sidebar-header {
                padding: 10px;
                font-weight: bold;
                border-bottom: 1px solid var(--border-color);
            }

            .search-container {
                padding: 10px;
                border-bottom: 1px solid var(--border-color);
            }

            .search-input {
                width: 100%;
                padding: var(--input-padding-vertical) var(--input-padding-horizontal);
                border: 1px solid var(--border-color);
                background-color: var(--input-background);
                color: var(--input-foreground);
                outline: none;
                box-sizing: border-box;
            }

            .search-input:focus {
                border-color: var(--highlight-color);
            }

            .method-list {
                flex: 1;
                overflow-y: auto;
                padding: 0;
            }

            .method-category {
                margin: 0;
                padding: 0;
            }

            .category-header {
                padding: 8px 10px;
                font-weight: bold;
                cursor: pointer;
                user-select: none;
                border-bottom: 1px solid var(--border-color);
                position: relative;
            }

            .category-header::after {
                content: "▼";
                position: absolute;
                right: 10px;
                transition: transform 0.2s;
            }

            .category-header.collapsed::after {
                transform: rotate(-90deg);
            }

            .category-items {
                max-height: 1000px;
                overflow: hidden;
                transition: max-height 0.3s ease-in-out;
            }

            .category-items.collapsed {
                max-height: 0;
            }

            .method-item {
                padding: 6px 10px 6px 20px;
                cursor: pointer;
                border-bottom: 1px solid var(--border-color);
            }

            .method-item:hover {
                background-color: rgba(0, 0, 0, 0.1);
            }

            .method-item.selected {
                background-color: var(--highlight-color);
                color: #ffffff;
            }

            .content {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
            }

            .content-header {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid var(--border-color);
            }

            .description-box {
                background-color: var(--description-background);
                color: var(--description-foreground);
                padding: 10px;
                margin-bottom: 20px;
                border-radius: 3px;
                display: flex;
                align-items: flex-start;
            }

            .info-icon {
                margin-right: 10px;
                font-size: 16px;
            }

            .form-group {
                margin-bottom: 15px;
            }

            .form-label {
                display: block;
                margin-bottom: 5px;
            }

            .form-input, .form-select {
                width: 100%;
                padding: var(--input-padding-vertical) var(--input-padding-horizontal);
                border: 1px solid var(--border-color);
                background-color: var(--input-background);
                color: var(--input-foreground);
                outline: none;
                box-sizing: border-box;
            }

            .form-input:focus, .form-select:focus {
                border-color: var(--highlight-color);
            }

            .form-checkbox {
                margin-right: 5px;
            }

            .button {
                padding: 6px 14px;
                border: none;
                cursor: pointer;
                border-radius: 2px;
            }

            .insert-button {
                background-color: var(--button-background);
                color: var(--button-foreground);
                font-weight: bold;
            }

            .insert-button:hover {
                background-color: var(--button-hover-background);
            }

            .close-button {
                background-color: transparent;
                color: var(--foreground-color);
                border: 1px solid var(--border-color);
            }

            .close-button:hover {
                background-color: rgba(0, 0, 0, 0.1);
            }

            .capture-button {
                background-color: var(--button-background);
                color: var(--button-foreground);
            }

            .capture-button:hover {
                background-color: var(--button-hover-background);
            }

            .helper-text {
                font-size: 12px;
                color: #888888;
                margin-top: 4px;
            }

            .form-container {
                margin-bottom: 15px;
            }

            .examples-section {
                margin: 15px 0;
                padding: 10px;
                background-color: var(--description-background);
                border-radius: 3px;
            }

            .examples-title {
                font-weight: bold;
                margin-bottom: 8px;
            }

            .examples-content {
                font-family: monospace;
                white-space: pre-wrap;
                font-size: 12px;
                background-color: rgba(0, 0, 0, 0.1);
                padding: 8px;
                border-radius: 3px;
                overflow-x: auto;
                margin: 0;
            }

            /* 隐藏初始内容 */
            .content-placeholder {
                display: flex;
                height: 100%;
                align-items: center;
                justify-content: center;
                color: #888888;
            }

            .method-details {
                display: none;
            }

            .footer {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                padding: 10px 20px;
                background-color: var(--sidebar-background);
                border-top: 1px solid var(--border-color);
            }

            .header.dragging {
                cursor: grabbing;
            }
        `;

        // 根据主题设置返回相应的 CSS
        return this.forceLightTheme ? lightThemeVars + commonStyles : darkThemeVars + commonStyles;
    }

    override dispose(): void {
        super.dispose();
    }
}
