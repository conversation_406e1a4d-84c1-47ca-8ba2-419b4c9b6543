/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2023-2024 <PERSON><PERSON><PERSON>. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../../base/common/lifecycle.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { localize } from '../../../../../nls.js';
import { Emitter } from '../../../../../base/common/event.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { ITextFileService } from '../../../../services/textfile/common/textfiles.js';
import { URI } from '../../../../../base/common/uri.js';

/**
 * 操作记录数据结构
 */
export interface OperationRecord {
	id: string;
	timestamp: number;
	type: 'mouse' | 'mouse_click' | 'mouse_right_click' | 'mouse_double_click' | 'mouse_drag' | 'mouse_hover' | 'keyboard' | 'window' | 'menu';
	action: string;
	target: {
		name?: string;
		type?: string;
		position?: { x: number; y: number };
		text?: string;
	};
	details: any;
	widget_info?: any;
	stepIndex?: number; // 所属步骤索引
	stepName?: string;  // 所属步骤名称
}

/**
 * 操作记录窗口
 * 显示录制过程中的操作记录，类似图片中的样式
 */
export class OperationRecordWindow extends Disposable {
	// 操作记录窗口
	private recordWindow: Window | null = null;

	// 操作记录列表
	private operations: OperationRecord[] = [];

	// 全流程录制状态
	private isFullRecording = false;





	// 当前测试用例信息
	private currentTestCase: any = null;

	// 当前步骤索引
	private currentStepIndex = 0;

	// YAML文件中的步骤信息
	private yamlSteps: string[] = [];

	// 录制状态
	private recordingStatus: 'idle' | 'starting' | 'recording' | 'paused' = 'idle';

	// 事件发射器
	private readonly _onOperationAdded = new Emitter<OperationRecord>();
	readonly onOperationAdded = this._onOperationAdded.event;

	private readonly _onWindowClosed = new Emitter<void>();
	readonly onWindowClosed = this._onWindowClosed.event;

	private readonly _onFullRecordingToggled = new Emitter<boolean>();
	readonly onFullRecordingToggled = this._onFullRecordingToggled.event;

	// 性能优化相关属性
	private updateScheduled: boolean = false;
	private batchUpdateTimer: any = null;
	private readonly BATCH_UPDATE_DELAY = 16; // 16ms ≈ 60fps
	private lastUpdateTime: number = 0;
	private readonly MIN_UPDATE_INTERVAL = 50; // 最小更新间隔50ms

	constructor(
		private readonly logService: ILogService,
		private readonly notificationService: INotificationService,
		private readonly fileService: IFileService,
		private readonly textFileService: ITextFileService
	) {
		super();
	}

	/**
	 * 显示操作记录窗口
	 */
	public async show(): Promise<void> {
		// 先隐藏现有窗口
		this.hide();

		try {
			this.logService.info('开始创建操作记录窗口...');

			// 窗口配置
			const windowWidth = 400;
			const windowHeight = 500;
			const windowLeft = Math.max(0, Math.floor((window.screen.width - windowWidth) / 2));
			const windowTop = Math.max(0, Math.floor((window.screen.height - windowHeight) / 4));

			// 创建窗口
			const features = `width=${windowWidth},height=${windowHeight},left=${windowLeft},top=${windowTop},resizable=true,minimizable=true,maximizable=false`;
			this.recordWindow = window.open('', 'operationRecord', features);

			if (!this.recordWindow) {
				throw new Error('无法创建操作记录窗口');
			}

			// 设置窗口内容
			this.setupWindowContent();

			// 监听窗口关闭事件
			this.recordWindow.addEventListener('beforeunload', () => {
				this._onWindowClosed.fire();
			});

			// 发送窗口位置信息给后端
			this.sendWindowBoundsToBackend();

			// 监听窗口移动事件，实时更新位置信息
			this.setupWindowMoveListener();

			this.logService.info('操作记录窗口创建成功');
		} catch (error) {
			this.logService.error(`创建操作记录窗口时出错: ${error instanceof Error ? error.message : String(error)}`);
			this.notificationService.error(localize('operationRecordWindowError', "创建操作记录窗口时出错"));
		}
	}

	/**
	 * 隐藏操作记录窗口
	 */
	public hide(): void {
		// 清理性能优化相关的定时器
		this.cleanupPerformanceOptimizations();

		if (this.recordWindow) {
			try {
				// 清理主题监听器
				const observer = (this.recordWindow as any)._themeObserver;
				if (observer) {
					observer.disconnect();
					this.logService.info('记录器窗口主题监听器已清理');
				}

				this.recordWindow.close();
			} catch (e) {
				// 忽略错误
			}
			this.recordWindow = null;
		}
	}

	/**
	 * 清理性能优化相关资源
	 */
	private cleanupPerformanceOptimizations(): void {
		if (this.batchUpdateTimer) {
			clearTimeout(this.batchUpdateTimer);
			this.batchUpdateTimer = null;
		}
		this.updateScheduled = false;
	}

	/**
	 * 添加操作记录（性能优化版本）
	 */
	public addOperation(operation: OperationRecord): void {
		// 检查是否为重复事件
		if (this.isDuplicateOperation(operation)) {
			this.logService.info(`跳过重复事件: ${operation.action} 位置(${operation.target?.position?.x}, ${operation.target?.position?.y})`);
			return;
		}

		// 自动设置当前步骤信息
		if (operation.stepIndex === undefined) {
			operation.stepIndex = this.currentStepIndex;
		}
		if (operation.stepName === undefined && this.yamlSteps.length > this.currentStepIndex) {
			operation.stepName = this.yamlSteps[this.currentStepIndex];
		}

		// 添加到操作数组
		this.operations.push(operation);
		this._onOperationAdded.fire(operation);

		// 使用批量更新策略
		this.scheduleUpdate(operation);
	}

	/**
	 * 批量添加操作记录（性能优化）
	 */
	public addOperations(operations: OperationRecord[]): void {
		if (operations.length === 0) {
			return;
		}

		// 批量处理操作
		const validOperations: OperationRecord[] = [];

		for (const operation of operations) {
			// 检查是否为重复事件
			if (this.isDuplicateOperation(operation)) {
				continue;
			}

			// 自动设置当前步骤信息
			if (operation.stepIndex === undefined) {
				operation.stepIndex = this.currentStepIndex;
			}
			if (operation.stepName === undefined && this.yamlSteps.length > this.currentStepIndex) {
				operation.stepName = this.yamlSteps[this.currentStepIndex];
			}

			validOperations.push(operation);
		}

		if (validOperations.length === 0) {
			return;
		}

		// 批量添加到数组
		this.operations.push(...validOperations);

		// 触发事件（批量）
		validOperations.forEach(operation => {
			this._onOperationAdded.fire(operation);
		});

		// 延迟更新UI（避免频繁DOM操作）
		this.scheduleUpdate();
	}

	/**
	 * 调度更新（性能优化）
	 */
	private scheduleUpdate(operation?: OperationRecord): void {
		const now = Date.now();

		// 如果是第一个操作、前几个操作或者是重要操作，立即更新
		if (this.operations.length <= 3 || this.isImportantOperation(operation)) {
			this.performUpdate();
			return;
		}

		// 如果距离上次更新时间太短，使用批量更新
		if (now - this.lastUpdateTime < this.MIN_UPDATE_INTERVAL) {
			if (!this.updateScheduled) {
				this.updateScheduled = true;
				this.batchUpdateTimer = setTimeout(() => {
					this.performUpdate();
				}, this.BATCH_UPDATE_DELAY);
			}
		} else {
			// 立即更新
			this.performUpdate();
		}
	}

	/**
	 * 执行实际的更新操作
	 */
	private performUpdate(): void {
		if (this.batchUpdateTimer) {
			clearTimeout(this.batchUpdateTimer);
			this.batchUpdateTimer = null;
		}

		this.updateScheduled = false;
		this.lastUpdateTime = Date.now();

		// 执行DOM更新
		this.updateOperationList();
	}

	/**
	 * 判断是否为重要操作（需要立即更新）
	 */
	private isImportantOperation(operation?: OperationRecord): boolean {
		if (!operation) return false;

		// 录制状态变化事件需要立即更新
		return operation.action === '录制开始' ||
			operation.action === '录制完成' ||
			operation.action === '录制统计' ||
			operation.details?.isRecordingStart ||
			operation.details?.isRecordingComplete ||
			operation.details?.isRecordingResult;
	}

	/**
	 * 检查是否为重复操作
	 */
	private isDuplicateOperation(operation: OperationRecord): boolean {
		// 检查最近的几个操作（最多检查最近5个）
		const recentOperations = this.operations.slice(-5);

		for (const recentOp of recentOperations) {
			// 检查是否为相同类型的操作
			if (recentOp.type === operation.type && recentOp.action === operation.action) {
				// 检查时间间隔（如果时间间隔小于100ms，认为是重复事件）
				const timeDiff = operation.timestamp - recentOp.timestamp;
				if (timeDiff < 100) {
					// 检查位置是否相同（对于鼠标事件）
					if (operation.target?.position && recentOp.target?.position) {
						const positionDiff = Math.abs(operation.target.position.x - recentOp.target.position.x) +
							Math.abs(operation.target.position.y - recentOp.target.position.y);
						if (positionDiff < 5) { // 位置差异小于5像素
							return true;
						}
					} else {
						// 对于非鼠标事件，只检查时间和类型
						return true;
					}
				}
			}
		}

		return false;
	}

	/**
	 * 添加公共方法节点到录制列表
	 * @param methodName 方法名
	 * @param formattedCode 格式化的YAML代码
	 * @param stepIndex 目标步骤索引
	 * @param stepName 目标步骤名称
	 */
	public addCommonMethodNode(methodName: string, formattedCode: string, stepIndex?: number, stepName?: string): void {
		const targetStepIndex = stepIndex !== undefined ? stepIndex : this.currentStepIndex;

		// 改进步骤名称获取逻辑
		let targetStepName = stepName;

		if (!targetStepName) {
			// 首先尝试从yamlSteps获取
			if (this.yamlSteps.length > targetStepIndex) {
				targetStepName = this.yamlSteps[targetStepIndex];
			}

			// 如果yamlSteps中没有，尝试从当前测试用例的步骤信息获取
			if (!targetStepName && this.currentTestCase) {
				const steps = this.currentTestCase.steps || this.currentTestCase.TestCaseSteps || this.currentTestCase.testCaseSteps || [];
				if (steps[targetStepIndex]) {
					const step = steps[targetStepIndex];
					if (typeof step === 'string') {
						targetStepName = `步骤${targetStepIndex + 1}：${step}`;
					} else if (step && typeof step === 'object') {
						const desc = step.desc || step.description || step.name || step.title || step.value || '';
						if (desc && desc.trim()) {
							targetStepName = `步骤${step.index || targetStepIndex + 1}：${desc.trim()}`;
						}
					}
				}
			}

			// 最后的备选方案
			if (!targetStepName) {
				targetStepName = `步骤${targetStepIndex + 1}`;
			}
		}

		const operation: OperationRecord = {
			id: `method_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
			timestamp: Date.now(),
			type: 'menu', // 使用menu类型表示这是一个方法插入操作
			action: `插入公共方法: ${methodName}`,
			target: {
				name: methodName,
				type: 'common_method',
				text: formattedCode
			},
			details: {
				methodName: methodName,
				formattedCode: formattedCode,
				isCommonMethod: true
			},
			stepIndex: targetStepIndex,
			stepName: targetStepName
		};

		this.operations.push(operation);
		this._onOperationAdded.fire(operation);

		// 更新窗口显示
		this.updateOperationList();

		this.logService.info(`已添加公共方法节点: ${methodName} 到步骤 ${targetStepName}`);
	}

	/**
	 * 清空操作记录
	 */
	public clearOperations(): void {
		this.operations = [];
		this.updateOperationList();
	}

	/**
	 * 获取所有操作记录
	 */
	public getOperations(): OperationRecord[] {
		return [...this.operations];
	}

	/**
	 * 根据索引移除操作记录
	 * 用于优化双击事件，移除多余的单击事件
	 */
	public removeOperationByIndex(index: number): boolean {
		try {
			if (index >= 0 && index < this.operations.length) {
				const removedOperation = this.operations[index];
				this.operations.splice(index, 1);

				// 更新操作列表显示
				this.updateOperationList();

				this.logService.info(`移除了索引为 ${index} 的操作记录: ${removedOperation.action}`);
				return true;
			}
			return false;
		} catch (error) {
			this.logService.error(`移除操作记录时出错: ${error}`);
			return false;
		}
	}

	/**
	 * 设置暂停状态
	 */
	public setPaused(isPaused: boolean): void {
		this.togglePause(isPaused);
	}

	/**
	 * 切换全流程录制状态
	 */
	private toggleFullRecording(): void {
		// 切换状态
		this.isFullRecording = !this.isFullRecording;

		// 更新UI（包括禁用按钮）
		this.updateFullRecordingUI();

		// 触发状态变更事件
		this._onFullRecordingToggled.fire(this.isFullRecording);
	}

	/**
	 * 更新全流程录制按钮UI
	 */
	private updateFullRecordingUI(): void {
		if (!this.recordWindow) {
			return;
		}

		const fullRecordBtn = this.recordWindow.document.getElementById('fullRecordBtn');
		if (!fullRecordBtn) {
			return;
		}

		if (this.isFullRecording) {
			// 全流程录制已启动：按钮置灰禁用
			fullRecordBtn.classList.add('active');
			fullRecordBtn.classList.add('disabled');
			fullRecordBtn.title = '全流程录制已启动（停止录制后可重新启用）';
		} else {
			// 全流程录制未启动：按钮可用
			fullRecordBtn.classList.remove('active');
			fullRecordBtn.classList.remove('disabled');
			fullRecordBtn.classList.remove('processing');
			fullRecordBtn.title = '全流程录制';
		}
	}

	/**
	 * 获取全流程录制状态
	 */
	public isFullRecordingActive(): boolean {
		return this.isFullRecording;
	}

	/**
	 * 设置全流程录制状态
	 */
	public setFullRecording(isActive: boolean): void {
		if (this.isFullRecording !== isActive) {
			this.isFullRecording = isActive;
			this.updateFullRecordingUI();
		}
	}



	/**
	 * 获取录制时长设置
	 */
	public getRecordingDuration(): number {
		return this.getStoredRecordingDuration();
	}

	/**
	 * 设置当前测试用例信息
	 */
	public setCurrentTestCase(testCase: any): void {
		this.currentTestCase = testCase;
		this.currentStepIndex = 0;
		// 异步加载YAML文件中的步骤信息
		this.loadYamlSteps(testCase);
	}

	/**
	 * 设置录制状态
	 */
	public setRecordingStatus(status: 'idle' | 'starting' | 'recording' | 'paused'): void {
		this.recordingStatus = status;

		// 根据状态更新显示
		switch (status) {
			case 'idle':
				this.showEmptyState();
				break;
			case 'starting':
				this.showRecordingStatus('starting');
				break;
			case 'recording':
				// 如果没有操作记录，显示录制提示
				if (this.operations.length === 0) {
					this.showRecordingStatus('recording');
				} else {
					this.updateOperationList();
				}
				break;
			case 'paused':
				this.showRecordingStatus('paused');
				break;
		}
	}

	/**
	 * 获取当前录制状态
	 */
	public getRecordingStatus(): 'idle' | 'starting' | 'recording' | 'paused' {
		return this.recordingStatus;
	}

	/**
	 * 设置当前步骤索引
	 */
	public setCurrentStepIndex(stepIndex: number): void {
		this.currentStepIndex = stepIndex;
		this.updateStepDisplay();
	}

	/**
	 * 从YAML文件加载步骤信息
	 */
	private async loadYamlSteps(testCase: any): Promise<void> {
		try {
			// 重置步骤信息
			this.yamlSteps = [];

			// 检查是否有YAML文件路径
			if (!testCase.ymlPath) {
				this.logService.warn('测试用例没有YAML文件路径，使用TestCaseSteps作为备选');
				this.updateStepDisplay();
				return;
			}

			// 读取YAML文件内容
			const yamlUri = URI.parse(testCase.ymlPath);
			const exists = await this.fileService.exists(yamlUri);

			if (!exists) {
				this.logService.warn(`YAML文件不存在: ${testCase.ymlPath}`);
				this.updateStepDisplay();
				return;
			}

			const fileContent = await this.textFileService.read(yamlUri);
			const yamlContent = fileContent.value;

			// 解析YAML中的步骤
			this.parseYamlSteps(yamlContent);

			// 更新步骤显示
			this.updateStepDisplay();

		} catch (error) {
			this.logService.error(`加载YAML步骤失败: ${error}`);
			// 出错时使用TestCaseSteps作为备选
			this.updateStepDisplay();
		}
	}

	/**
	 * 解析YAML内容中的步骤信息
	 */
	private parseYamlSteps(yamlContent: string): void {
		try {
			// 使用正则表达式匹配步骤行
			const stepRegex = /^\s*-\s+(步骤\d+[：:][^:]+):/gm;
			const matches = yamlContent.matchAll(stepRegex);

			this.yamlSteps = [];
			for (const match of matches) {
				if (match[1]) {
					this.yamlSteps.push(match[1].trim());
				}
			}

			console.log('解析到的YAML步骤:', this.yamlSteps);
			this.logService.info(`从YAML解析到 ${this.yamlSteps.length} 个步骤`);

		} catch (error) {
			this.logService.error(`解析YAML步骤失败: ${error}`);
			this.yamlSteps = [];
		}
	}

	/**
	 * 更新步骤显示
	 */
	private updateStepDisplay(): void {
		if (!this.recordWindow) {
			return;
		}

		const stepSelect = this.recordWindow.document.getElementById('stepSelect') as HTMLSelectElement;
		if (!stepSelect) {
			return;
		}

		// 清空现有选项（保留默认选项）
		while (stepSelect.children.length > 1) {
			stepSelect.removeChild(stepSelect.lastChild!);
		}

		// 优先使用YAML文件中解析的步骤信息
		if (this.yamlSteps && this.yamlSteps.length > 0) {
			console.log('使用YAML步骤信息:', this.yamlSteps);
			this.yamlSteps.forEach((stepText: string, index: number) => {
				const option = this.recordWindow!.document.createElement('option');
				option.value = index.toString();
				option.textContent = stepText;
				stepSelect.appendChild(option);
			});
		} else {
			// 备选：尝试从不同字段获取步骤信息
			let steps: any[] = [];

			if (this.currentTestCase) {
				// 尝试多个可能的步骤字段
				if (this.currentTestCase.steps && Array.isArray(this.currentTestCase.steps)) {
					steps = this.currentTestCase.steps;
				} else if (this.currentTestCase.TestCaseSteps && Array.isArray(this.currentTestCase.TestCaseSteps)) {
					steps = this.currentTestCase.TestCaseSteps;
				} else if (this.currentTestCase.testCaseSteps && Array.isArray(this.currentTestCase.testCaseSteps)) {
					steps = this.currentTestCase.testCaseSteps;
				}
			}

			// 记录调试信息
			console.log('当前测试用例信息:', this.currentTestCase);
			console.log('找到的步骤信息:', steps);
			this.logService.info(`录制控制窗口 - 当前测试用例: ${JSON.stringify(this.currentTestCase)}`);
			this.logService.info(`录制控制窗口 - 找到的步骤信息: ${JSON.stringify(steps)}`);

			// 添加步骤选项
			if (steps && steps.length > 0) {
				steps.forEach((step: any, index: number) => {
					const option = this.recordWindow!.document.createElement('option');
					option.value = index.toString();

					let stepText = '';
					this.logService.info(`处理步骤 ${index}: ${JSON.stringify(step)}`);

					if (typeof step === 'string') {
						stepText = `步骤${index + 1}：${step}`;
						this.logService.info(`步骤 ${index} 是字符串类型: ${stepText}`);
					} else if (step && typeof step === 'object') {
						// 处理YAML格式的步骤（如 "步骤1：Gat启动应用"）
						const stepKey = Object.keys(step)[0];
						if (stepKey && stepKey !== 'index' && stepKey !== 'desc' && stepKey !== 'expect') {
							// 如果key不是常见的属性字段，则认为是YAML格式的步骤名
							stepText = stepKey;
							this.logService.info(`步骤 ${index} 使用YAML格式的key: ${stepText}`);
						} else {
							// 处理测试用例中的步骤对象格式
							// 优先使用desc字段，这是步骤的实际内容
							const desc = step.desc || step.description || step.name || step.title || step.value || '';
							const expect = step.expect || step.expected || '';

							if (desc && desc.trim()) {
								// 如果有期望结果，也显示出来
								if (expect && expect.trim()) {
									stepText = `步骤${step.index || index + 1}：${desc.trim()} (期望: ${expect.trim()})`;
								} else {
									stepText = `步骤${step.index || index + 1}：${desc.trim()}`;
								}
								this.logService.info(`步骤 ${index} 使用desc字段: ${stepText}`);
							} else {
								stepText = `步骤${step.index || index + 1}：未知步骤`;
								this.logService.info(`步骤 ${index} 没有找到有效描述，使用默认: ${stepText}`);
							}
						}
					} else {
						stepText = `步骤${index + 1}：未知步骤`;
						this.logService.info(`步骤 ${index} 类型未知，使用默认: ${stepText}`);
					}

					option.textContent = stepText;
					stepSelect.appendChild(option);
				});
			} else {
				// 如果没有步骤信息，添加默认步骤
				const option = this.recordWindow.document.createElement('option');
				option.value = '0';
				option.textContent = '步骤1：开始录制操作';
				stepSelect.appendChild(option);
			}
		}

		// 设置当前选中的步骤
		stepSelect.value = this.currentStepIndex.toString();
	}

	/**
	 * 设置窗口内容
	 */
	private setupWindowContent(): void {
		if (!this.recordWindow) {
			return;
		}

		const doc = this.recordWindow.document;

		// 设置文档标题
		doc.title = '记录器';

		// 添加元数据
		const metaCharset = doc.createElement('meta');
		metaCharset.setAttribute('charset', 'UTF-8');
		doc.head.appendChild(metaCharset);

		const metaViewport = doc.createElement('meta');
		metaViewport.setAttribute('name', 'viewport');
		metaViewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
		doc.head.appendChild(metaViewport);

		// 添加样式
		this.addStyles(doc);

		// 应用主题
		this.applyTheme(doc);

		// 设置主题监听器
		this.setupThemeListener(doc);

		// 创建HTML结构
		this.createWindowStructure(doc);

		// 添加事件监听器
		this.addEventListeners(doc);
	}

	/**
	 * 添加样式
	 */
	private addStyles(doc: Document): void {
		const style = doc.createElement('style');
		style.textContent = `
			:root {
				/* 主题变量将通过JavaScript动态设置 */
				--window-header-bg: #2d2d30;
				--window-header-fg: #cccccc;
				--window-header-border: #3c3c3c;
				--window-bg: #1e1e1e;
				--window-fg: #cccccc;
				--toolbar-bg: #252526;
				--toolbar-border: #3c3c3c;
			}

			* {
				margin: 0;
				padding: 0;
				box-sizing: border-box;
			}

			body {
				font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
				font-size: 12px;
				background-color: var(--window-bg);
				color: var(--window-fg);
				overflow: hidden;
			}

			.window-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 8px 12px;
				background-color: var(--window-header-bg);
				border-bottom: 1px solid var(--window-header-border);
				height: 40px;
				cursor: move;
				user-select: none;
			}

			.window-title {
				font-size: 14px;
				font-weight: 500;
				color: var(--window-header-fg);
			}

			.window-controls {
				display: flex;
				gap: 4px;
			}

			.control-btn {
				width: 20px;
				height: 20px;
				border: none;
				border-radius: 3px;
				cursor: pointer;
				font-size: 12px;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.btn-minimize {
				background-color: #ffbd2e;
				color: #333;
			}

			.btn-close {
				background-color: #ff5f57;
				color: #fff;
			}

			.toolbar {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 8px 12px;
				background-color: var(--toolbar-bg);
				border-bottom: 1px solid var(--toolbar-border);
				height: 40px;
			}

			.recording-indicator {
				display: flex;
				align-items: center;
				gap: 6px;
				color: var(--window-fg);
				font-size: 12px;
				font-weight: 500;
			}

			.recording-dot {
				width: 8px;
				height: 8px;
				border-radius: 50%;
				background-color: #ff3b30;
				animation: pulse 1.5s infinite;
			}

			.toolbar.paused .recording-dot {
				animation: none;
				background-color: #ffcc00;
			}

			@keyframes pulse {
				0% { opacity: 1; }
				50% { opacity: 0.3; }
				100% { opacity: 1; }
			}

			.toolbar-controls {
				display: flex;
				align-items: center;
				gap: 8px;
			}

			.toolbar-btn {
				width: 28px;
				height: 28px;
				border-radius: 4px;
				background-color: var(--button-bg);
				border: 1px solid var(--button-border);
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: all 0.2s;
			}

			.toolbar-btn:hover {
				background-color: var(--button-hover-bg);
				border-color: var(--button-border);
			}

			.toolbar-btn.disabled {
				opacity: 0.5;
				cursor: not-allowed;
				pointer-events: none;
			}

			.toolbar-btn svg {
				width: 16px;
				height: 16px;
				fill: var(--window-fg);
			}

			.toolbar-btn.pause svg {
				fill: #ff9500;
			}

			.toolbar-btn.play svg {
				fill: #34c759;
			}

			.toolbar-btn.insert svg {
				fill: #007aff;
			}

			.toolbar-btn.settings svg {
				fill: #8e8e93;
			}

			.toolbar-btn.settings:hover svg {
				fill: #007aff;
			}

			.toolbar-btn.full-record svg {
				fill: #32d74b;
			}

			.toolbar-btn.full-record.active {
				background-color: #32d74b;
				border-color: #32d74b;
			}

			.toolbar-btn.full-record.active svg {
				fill: #fff;
			}

			.toolbar-btn.full-record.active.disabled {
				background-color: #32d74b;
				border-color: #32d74b;
				opacity: 0.6;
				cursor: not-allowed;
				pointer-events: none;
			}

			.toolbar-btn.full-record.active.disabled svg {
				fill: #fff;
			}

			.toolbar-btn.stop svg {
				fill: #ff3b30;
			}

			.toolbar-btn.clear svg {
				fill: #ff9500;
			}

			.toolbar-btn.clear:hover {
				background-color: rgba(255, 149, 0, 0.1);
			}

			.step-container {
				background-color: var(--window-bg);
				border-bottom: 1px solid var(--toolbar-border);
				padding: 0;
			}

			.step-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 8px 12px;
				background-color: var(--list-item-bg);
				user-select: none;
			}

			.step-select {
				flex: 1;
				margin-right: 8px;
				padding: 4px 8px;
				font-size: 13px;
				font-weight: 500;
				color: var(--window-fg);
				background-color: var(--input-bg);
				border: 1px solid var(--input-border);
				border-radius: 3px;
				outline: none;
				cursor: pointer;
			}

			.step-select:hover {
				background-color: var(--input-hover-bg);
			}

			.step-select:focus {
				border-color: var(--focus-border);
				box-shadow: 0 0 0 1px var(--focus-border);
			}

			.step-select option {
				background-color: var(--dropdown-bg);
				color: var(--dropdown-fg);
				padding: 4px 8px;
			}

			.step-toggle {
				font-size: 12px;
				color: var(--text-description-color);
				transition: transform 0.2s ease;
			}

			.step-toggle.collapsed {
				transform: rotate(-90deg);
			}

			.operation-list-container {
				height: calc(100vh - 160px); /* 调整高度以适应步骤区域 */
				overflow-y: auto;
				padding: 8px;
				background-color: var(--window-bg);
			}

			.operation-item {
				background-color: var(--list-item-bg);
				border: 1px solid var(--list-item-border);
				border-radius: 4px;
				margin-bottom: 4px;
				padding: 8px;
				transition: box-shadow 0.2s;
				color: var(--window-fg);
				position: relative;
				min-height: 60px;
			}

			.operation-item:hover {
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
				background-color: var(--list-item-hover-bg);
			}

			/* 录制完成事件的特殊样式 */
			.operation-item.recording-complete {
				background-color: rgba(50, 215, 75, 0.1);
				border-color: #32d74b;
				border-width: 2px;
			}

			.operation-item.recording-complete .operation-type {
				color: #32d74b;
				font-weight: bold;
			}

			.operation-item.recording-complete .operation-action {
				color: #32d74b;
				font-weight: 500;
			}

			/* 录制统计事件的特殊样式 */
			.operation-item.recording-result {
				background-color: rgba(0, 122, 255, 0.1);
				border-color: #007aff;
				border-width: 2px;
			}

			.operation-item.recording-result .operation-type {
				color: #007aff;
				font-weight: bold;
			}

			.operation-item.recording-result .operation-action {
				color: #007aff;
				font-weight: 500;
			}

			/* 录制开始事件的特殊样式 */
			.operation-item.recording-start {
				background-color: rgba(255, 149, 0, 0.1);
				border-color: #ff9500;
				border-width: 2px;
			}

			.operation-item.recording-start .operation-type {
				color: #ff9500;
				font-weight: bold;
			}

			.operation-item.recording-start .operation-action {
				color: #ff9500;
				font-weight: 500;
			}

			/* 没有绑定UNI控件对象的鼠标事件的红色醒目样式 */
			.operation-item.mouse-event-no-widget {
				background-color: rgba(255, 59, 48, 0.15);
				border-color: #ff3b30;
				border-width: 2px;
				box-shadow: 0 0 8px rgba(255, 59, 48, 0.3);
			}

			.operation-item.mouse-event-no-widget .operation-type {
				color: #ff3b30;
				font-weight: bold;
			}

			.operation-item.mouse-event-no-widget .operation-action {
				color: #ff3b30;
				font-weight: 500;
			}

			.operation-item.mouse-event-no-widget .operation-target {
				background-color: rgba(255, 59, 48, 0.1);
				border: 1px solid #ff3b30;
				color: #ff3b30;
			}

			/* 步骤分组样式 */
			.step-container {
				margin-bottom: 12px;
			}

			.step-header {
				display: flex;
				align-items: center;
				padding: 8px 12px;
				background-color: var(--toolbar-bg);
				border: 1px solid var(--toolbar-border);
				border-radius: 6px 6px 0 0;
				cursor: pointer;
				user-select: none;
				transition: background-color 0.2s;
			}

			.step-header:hover {
				background-color: var(--button-hover-bg);
			}

			.step-container.collapsed .step-header {
				border-radius: 6px;
			}

			.step-toggle-icon {
				margin-right: 8px;
				font-size: 12px;
				color: var(--text-description-color);
				transition: transform 0.2s;
			}

			.step-title {
				flex: 1;
				font-weight: 500;
				color: var(--window-fg);
				font-size: 13px;
			}

			.operation-count {
				font-size: 11px;
				color: var(--text-description-color);
				background-color: var(--text-code-bg);
				padding: 2px 6px;
				border-radius: 10px;
				margin-left: 8px;
			}

			.step-operations {
				border: 1px solid var(--toolbar-border);
				border-top: none;
				border-radius: 0 0 6px 6px;
				padding: 8px;
				background-color: var(--window-bg);
			}

			.step-operations .operation-item {
				margin-left: 16px;
				margin-bottom: 6px;
			}

			.step-operations .operation-item:last-child {
				margin-bottom: 0;
			}

			.operation-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 4px;
				gap: 8px; /* 元素之间的间距 */
			}

			.operation-type {
				font-weight: 500;
				color: var(--text-link-color);
				flex-shrink: 0; /* 防止类型文本被压缩 */
			}

			.operation-time {
				font-size: 11px;
				color: var(--text-description-color);
			}

			.operation-action {
				font-size: 12px;
				color: var(--window-fg);
				margin-bottom: 2px;
				line-height: 1.3;
			}

			.operation-target {
				font-size: 10px;
				color: var(--text-description-color);
				background-color: var(--text-code-bg);
				padding: 2px 6px;
				border-radius: 3px;
				line-height: 1.2;
			}

			.operation-delete-btn {
				width: 18px;
				height: 18px;
				border: none;
				background-color: transparent;
				color: var(--text-description-color);
				cursor: pointer;
				border-radius: 3px;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				line-height: 1;
				opacity: 0.6;
				transition: all 0.2s;
				flex-shrink: 0; /* 防止按钮被压缩 */
			}

			.operation-delete-btn:hover {
				background-color: var(--list-error-foreground);
				color: white;
				opacity: 1;
			}

			.operation-delete-btn:active {
				transform: scale(0.95);
			}

			.footer {
				position: fixed;
				bottom: 0;
				left: 0;
				right: 0;
				height: 40px;
				background-color: var(--toolbar-bg);
				border-top: 1px solid var(--toolbar-border);
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 12px;
			}

			.footer-btn {
				padding: 6px 16px;
				border: 1px solid var(--text-link-color);
				border-radius: 4px;
				background-color: var(--text-link-color);
				color: var(--window-bg);
				cursor: pointer;
				font-size: 12px;
			}

			.footer-btn.secondary {
				background-color: var(--button-bg);
				color: var(--window-fg);
				border-color: var(--button-border);
			}

			.footer-btn:hover {
				opacity: 0.8;
			}

			.empty-state {
				text-align: center;
				padding: 40px 20px;
				color: var(--text-description-color);
			}

			/* 设置对话框样式 */
			.settings-modal {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.5);
				display: none;
				justify-content: center;
				align-items: center;
				z-index: 10000;
			}

			.settings-content {
				background: var(--window-bg);
				border-radius: 8px;
				width: 400px;
				max-width: 90%;
				box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
				color: var(--window-fg);
				border: 1px solid var(--toolbar-border);
			}

			.settings-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 16px 20px;
				border-bottom: 1px solid var(--toolbar-border);
				background-color: var(--toolbar-bg);
				border-radius: 8px 8px 0 0;
			}

			.settings-header h3 {
				margin: 0;
				font-size: 16px;
				font-weight: 600;
				color: var(--window-fg);
			}

			.close-button {
				background: none;
				border: none;
				color: var(--window-fg);
				font-size: 24px;
				cursor: pointer;
				padding: 0;
				width: 24px;
				height: 24px;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 4px;
			}

			.close-button:hover {
				background-color: var(--button-hover-bg);
			}

			.settings-body {
				padding: 20px;
			}

			.setting-item {
				margin-bottom: 20px;
			}

			.setting-item label {
				display: block;
				margin-bottom: 8px;
				font-weight: 500;
				color: var(--window-fg);
			}

			.setting-item input[type="number"] {
				width: 100%;
				padding: 8px 12px;
				border: 1px solid var(--toolbar-border);
				border-radius: 4px;
				background: var(--window-bg);
				color: var(--window-fg);
				font-size: 14px;
				box-sizing: border-box;
			}

			.setting-item input[type="number"]:focus {
				outline: none;
				border-color: #007acc;
				box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
			}

			.setting-description {
				display: block;
				margin-top: 6px;
				font-size: 12px;
				color: #969696;
				line-height: 1.4;
			}

			.settings-footer {
				display: flex;
				justify-content: flex-end;
				gap: 12px;
				padding: 16px 20px;
				border-top: 1px solid var(--toolbar-border);
				background-color: var(--toolbar-bg);
				border-radius: 0 0 8px 8px;
			}

			.settings-btn {
				padding: 8px 16px;
				border: none;
				border-radius: 4px;
				font-size: 14px;
				cursor: pointer;
				transition: background-color 0.2s;
			}

			.settings-btn.cancel {
				background: var(--button-bg);
				color: var(--window-fg);
				border: 1px solid var(--toolbar-border);
			}

			.settings-btn.cancel:hover {
				background: var(--button-hover-bg);
			}

			.settings-btn.save {
				background: #007acc;
				color: #ffffff;
			}

			.settings-btn.save:hover {
				background: #005a9e;
			}
		`;
		doc.head.appendChild(style);
	}

	/**
	 * 应用主题到窗口
	 */
	private applyTheme(doc: Document): void {
		const body = doc.body;
		if (!body) {
			return;
		}

		// 检测当前主题 - 更全面的检测逻辑
		const currentTheme = this.detectCurrentTheme();

		// 移除所有主题类
		body.classList.remove('vs', 'vs-light', 'vs-dark', 'hc-black', 'hc-light');

		// 应用当前主题类
		body.classList.add(currentTheme);

		// 动态更新CSS变量以匹配当前主题
		this.updateThemeVariables(doc, currentTheme);

		this.logService.info(`记录器窗口应用主题: ${currentTheme}`);
	}

	/**
	 * 检测当前主题
	 */
	private detectCurrentTheme(): string {
		const docElement = document.documentElement;
		const body = document.body;

		// 检查多个可能的类名位置
		const checkClasses = (element: Element) => {
			if (element.classList.contains('vs-dark')) return 'vs-dark';
			if (element.classList.contains('hc-black')) return 'hc-black';
			if (element.classList.contains('hc-light')) return 'hc-light';
			if (element.classList.contains('vs')) return 'vs';
			return null;
		};

		// 先检查 documentElement
		let theme = checkClasses(docElement);
		if (theme) {
			this.logService.info(`从 documentElement 检测到主题: ${theme}`);
			return theme;
		}

		// 再检查 body
		theme = checkClasses(body);
		if (theme) {
			this.logService.info(`从 body 检测到主题: ${theme}`);
			return theme;
		}

		// 检查工作台容器
		const workbench = document.querySelector('.monaco-workbench');
		if (workbench) {
			theme = checkClasses(workbench);
			if (theme) {
				this.logService.info(`从 workbench 检测到主题: ${theme}`);
				return theme;
			}
		}

		// 如果都没找到，默认使用浅色主题
		this.logService.info('未检测到主题，使用默认浅色主题');
		return 'vs';
	}

	/**
	 * 更新主题变量
	 */
	private updateThemeVariables(doc: Document, theme: string): void {
		const root = doc.documentElement;

		if (theme === 'vs' || theme === 'vs-light' || theme === 'hc-light') {
			// 浅色主题变量
			root.style.setProperty('--window-header-bg', '#f3f3f3');
			root.style.setProperty('--window-header-fg', '#333333');
			root.style.setProperty('--window-header-border', '#e0e0e0');
			root.style.setProperty('--window-bg', '#ffffff');
			root.style.setProperty('--window-fg', '#333333');
			root.style.setProperty('--toolbar-bg', '#f8f8f8');
			root.style.setProperty('--toolbar-border', '#e0e0e0');

			// 按钮和控件颜色
			root.style.setProperty('--button-bg', '#f0f0f0');
			root.style.setProperty('--button-border', '#d0d0d0');
			root.style.setProperty('--button-hover-bg', '#e0e0e0');

			// 列表项颜色
			root.style.setProperty('--list-item-bg', '#f8f8f8');
			root.style.setProperty('--list-item-hover-bg', '#e8e8e8');
			root.style.setProperty('--list-item-border', '#e0e0e0');

			// 文本颜色
			root.style.setProperty('--text-link-color', '#0066cc');
			root.style.setProperty('--text-description-color', '#666666');
			root.style.setProperty('--text-code-bg', '#f5f5f5');
		} else {
			// 深色主题变量 (vs-dark, hc-black)
			root.style.setProperty('--window-header-bg', '#2d2d30');
			root.style.setProperty('--window-header-fg', '#cccccc');
			root.style.setProperty('--window-header-border', '#3c3c3c');
			root.style.setProperty('--window-bg', '#1e1e1e');
			root.style.setProperty('--window-fg', '#cccccc');
			root.style.setProperty('--toolbar-bg', '#252526');
			root.style.setProperty('--toolbar-border', '#3c3c3c');

			// 按钮和控件颜色
			root.style.setProperty('--button-bg', '#3c3c3c');
			root.style.setProperty('--button-border', '#3c3c3c');
			root.style.setProperty('--button-hover-bg', '#4c4c4c');

			// 列表项颜色
			root.style.setProperty('--list-item-bg', '#37373d');
			root.style.setProperty('--list-item-hover-bg', '#2a2d2e');
			root.style.setProperty('--list-item-border', '#3c3c3c');

			// 文本颜色
			root.style.setProperty('--text-link-color', '#4fc1ff');
			root.style.setProperty('--text-description-color', '#cccccc99');
			root.style.setProperty('--text-code-bg', '#0e639c29');
		}
	}

	/**
	 * 设置主题监听器
	 */
	private setupThemeListener(doc: Document): void {
		// 创建一个MutationObserver来监听主窗口的主题变化
		const observer = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
					// 主题发生变化，重新应用主题
					this.applyTheme(doc);
				}
			});
		});

		// 监听主窗口document.documentElement的class属性变化
		observer.observe(document.documentElement, {
			attributes: true,
			attributeFilter: ['class']
		});

		// 将observer存储起来，以便窗口关闭时清理
		if (this.recordWindow) {
			(this.recordWindow as any)._themeObserver = observer;
		}

		this.logService.info('记录器窗口主题监听器已设置');
	}

	/**
	 * 创建窗口结构
	 */
	private createWindowStructure(doc: Document): void {
		// 创建主容器
		const container = doc.createElement('div');
		container.className = 'window-container';
		doc.body.appendChild(container);

		// 创建标题栏
		const header = doc.createElement('div');
		header.className = 'window-header';
		container.appendChild(header);

		const title = doc.createElement('div');
		title.className = 'window-title';
		title.textContent = '记录器';
		header.appendChild(title);

		const controls = doc.createElement('div');
		controls.className = 'window-controls';
		header.appendChild(controls);

		const minimizeBtn = doc.createElement('button');
		minimizeBtn.className = 'control-btn btn-minimize';
		minimizeBtn.textContent = '−';
		minimizeBtn.title = '最小化';
		controls.appendChild(minimizeBtn);

		const closeBtn = doc.createElement('button');
		closeBtn.className = 'control-btn btn-close';
		closeBtn.textContent = '×';
		closeBtn.title = '关闭';
		controls.appendChild(closeBtn);

		// 创建工具栏
		const toolbar = doc.createElement('div');
		toolbar.className = 'toolbar';
		toolbar.id = 'toolbar';
		container.appendChild(toolbar);

		// 创建录制指示器
		const recordingIndicator = doc.createElement('div');
		recordingIndicator.className = 'recording-indicator';
		toolbar.appendChild(recordingIndicator);

		const recordingDot = doc.createElement('div');
		recordingDot.className = 'recording-dot';
		recordingIndicator.appendChild(recordingDot);

		const recordingText = doc.createElement('span');
		recordingText.textContent = '正在录制';
		recordingText.id = 'recordingText';
		recordingIndicator.appendChild(recordingText);

		// 创建工具栏控制按钮
		const toolbarControls = doc.createElement('div');
		toolbarControls.className = 'toolbar-controls';
		toolbar.appendChild(toolbarControls);

		// 暂停按钮
		const pauseBtn = doc.createElement('div');
		pauseBtn.className = 'toolbar-btn pause';
		pauseBtn.title = '暂停录制';
		pauseBtn.id = 'pauseBtn';
		toolbarControls.appendChild(pauseBtn);

		const pauseSvg = doc.createElementNS('http://www.w3.org/2000/svg', 'svg');
		pauseSvg.setAttribute('viewBox', '0 0 24 24');
		const pausePath = doc.createElementNS('http://www.w3.org/2000/svg', 'path');
		pausePath.setAttribute('d', 'M6 19h4V5H6v14zm8-14v14h4V5h-4z');
		pauseSvg.appendChild(pausePath);
		pauseBtn.appendChild(pauseSvg);

		// 继续按钮
		const playBtn = doc.createElement('div');
		playBtn.className = 'toolbar-btn play';
		playBtn.title = '继续录制';
		playBtn.id = 'playBtn';
		playBtn.style.display = 'none';
		toolbarControls.appendChild(playBtn);

		const playSvg = doc.createElementNS('http://www.w3.org/2000/svg', 'svg');
		playSvg.setAttribute('viewBox', '0 0 24 24');
		const playPath = doc.createElementNS('http://www.w3.org/2000/svg', 'path');
		playPath.setAttribute('d', 'M8 5v14l11-7z');
		playSvg.appendChild(playPath);
		playBtn.appendChild(playSvg);

		// 插入公共方法按钮
		const insertBtn = doc.createElement('div');
		insertBtn.className = 'toolbar-btn insert';
		insertBtn.title = '插入公共方法';
		insertBtn.id = 'insertBtn';
		toolbarControls.appendChild(insertBtn);

		const insertSvg = doc.createElementNS('http://www.w3.org/2000/svg', 'svg');
		insertSvg.setAttribute('viewBox', '0 0 24 24');
		const insertPath = doc.createElementNS('http://www.w3.org/2000/svg', 'path');
		insertPath.setAttribute('d', 'M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10h-4v4h-2v-4H7v-2h4V7h2v4h4v2z');
		insertSvg.appendChild(insertPath);
		insertBtn.appendChild(insertSvg);

		// 设置按钮
		const settingsBtn = doc.createElement('div');
		settingsBtn.className = 'toolbar-btn settings';
		settingsBtn.title = '设置';
		settingsBtn.id = 'settingsBtn';
		toolbarControls.appendChild(settingsBtn);

		const settingsSvg = doc.createElementNS('http://www.w3.org/2000/svg', 'svg');
		settingsSvg.setAttribute('viewBox', '0 0 24 24');
		const settingsPath = doc.createElementNS('http://www.w3.org/2000/svg', 'path');
		settingsPath.setAttribute('d', 'M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z');
		settingsSvg.appendChild(settingsPath);
		settingsBtn.appendChild(settingsSvg);

		// 全流程录制按钮
		const fullRecordBtn = doc.createElement('div');
		fullRecordBtn.className = 'toolbar-btn full-record';
		fullRecordBtn.title = '全流程录制';
		fullRecordBtn.id = 'fullRecordBtn';
		toolbarControls.appendChild(fullRecordBtn);

		const fullRecordSvg = doc.createElementNS('http://www.w3.org/2000/svg', 'svg');
		fullRecordSvg.setAttribute('viewBox', '0 0 24 24');
		const fullRecordPath = doc.createElementNS('http://www.w3.org/2000/svg', 'path');
		fullRecordPath.setAttribute('d', 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z');
		fullRecordSvg.appendChild(fullRecordPath);
		fullRecordBtn.appendChild(fullRecordSvg);

		// 停止按钮
		const stopBtn = doc.createElement('div');
		stopBtn.className = 'toolbar-btn stop';
		stopBtn.title = '停止录制';
		stopBtn.id = 'stopBtn';
		toolbarControls.appendChild(stopBtn);

		const stopSvg = doc.createElementNS('http://www.w3.org/2000/svg', 'svg');
		stopSvg.setAttribute('viewBox', '0 0 24 24');
		const stopPath = doc.createElementNS('http://www.w3.org/2000/svg', 'path');
		stopPath.setAttribute('d', 'M6 6h12v12H6z');
		stopSvg.appendChild(stopPath);
		stopBtn.appendChild(stopSvg);

		// 清空按钮
		const clearBtn = doc.createElement('div');
		clearBtn.className = 'toolbar-btn clear';
		clearBtn.title = '清空所有操作记录';
		clearBtn.id = 'clearBtn';
		toolbarControls.appendChild(clearBtn);

		const clearSvg = doc.createElementNS('http://www.w3.org/2000/svg', 'svg');
		clearSvg.setAttribute('viewBox', '0 0 24 24');
		const clearPath = doc.createElementNS('http://www.w3.org/2000/svg', 'path');
		clearPath.setAttribute('d', 'M3 6h18v2H3V6zm2 3h14v11c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2V9zm3 2v7h2v-7H8zm4 0v7h2v-7h-2z');
		clearSvg.appendChild(clearPath);
		clearBtn.appendChild(clearSvg);

		// 创建步骤显示区域
		const stepContainer = doc.createElement('div');
		stepContainer.className = 'step-container';
		stepContainer.id = 'stepContainer';
		container.appendChild(stepContainer);

		const stepHeader = doc.createElement('div');
		stepHeader.className = 'step-header';
		stepContainer.appendChild(stepHeader);

		// 创建步骤选择下拉框
		const stepSelect = doc.createElement('select');
		stepSelect.className = 'step-select';
		stepSelect.id = 'stepSelect';
		stepSelect.title = '选择当前步骤';
		// 添加默认选项
		const defaultOption = doc.createElement('option');
		defaultOption.value = '-1';
		defaultOption.textContent = '选择步骤...';
		stepSelect.appendChild(defaultOption);
		stepHeader.appendChild(stepSelect);

		const stepToggle = doc.createElement('div');
		stepToggle.className = 'step-toggle';
		stepToggle.id = 'stepToggle';
		stepToggle.textContent = '▼';
		stepToggle.title = '展开/收起步骤';
		stepHeader.appendChild(stepToggle);

		// 创建操作列表容器
		const listContainer = doc.createElement('div');
		listContainer.className = 'operation-list-container';
		listContainer.id = 'operationListContainer';
		container.appendChild(listContainer);

		// 创建底部按钮区域
		const footer = doc.createElement('div');
		footer.className = 'footer';
		container.appendChild(footer);

		const cancelBtn = doc.createElement('button');
		cancelBtn.className = 'footer-btn secondary';
		cancelBtn.textContent = '取消';
		cancelBtn.id = 'cancelBtn';
		footer.appendChild(cancelBtn);

		const completeBtn = doc.createElement('button');
		completeBtn.className = 'footer-btn';
		completeBtn.textContent = '完成';
		completeBtn.id = 'completeBtn';
		footer.appendChild(completeBtn);

		// 创建设置对话框
		this.createSettingsModal(doc, container);

		// 初始显示空状态
		this.showEmptyState();
	}

	/**
	 * 添加事件监听器
	 */
	private addEventListeners(doc: Document): void {
		// 最小化按钮
		const minimizeBtn = doc.querySelector('.btn-minimize');
		if (minimizeBtn) {
			minimizeBtn.addEventListener('click', () => {
				if (this.recordWindow) {
					this.recordWindow.blur();
				}
			});
		}

		// 关闭按钮
		const closeBtn = doc.querySelector('.btn-close');
		if (closeBtn) {
			closeBtn.addEventListener('click', () => {
				this.hide();
			});
		}

		// 取消按钮
		const cancelBtn = doc.getElementById('cancelBtn');
		if (cancelBtn) {
			cancelBtn.addEventListener('click', () => {
				// 发送取消录制消息
				window.postMessage({ type: 'gat:stopRecording' }, '*');
				this.hide();
			});
		}

		// 完成按钮
		const completeBtn = doc.getElementById('completeBtn');
		if (completeBtn) {
			completeBtn.addEventListener('click', () => {
				// 发送完成录制消息
				window.postMessage({ type: 'gat:stopRecording' }, '*');
				this.hide();
			});
		}

		// 步骤选择下拉框事件
		const stepSelect = doc.getElementById('stepSelect') as HTMLSelectElement;
		const stepToggle = doc.getElementById('stepToggle');
		const listContainer = doc.getElementById('operationListContainer');

		if (stepSelect) {
			stepSelect.addEventListener('change', () => {
				const selectedIndex = parseInt(stepSelect.value);
				if (selectedIndex >= 0) {
					this.setCurrentStepIndex(selectedIndex);
				}
			});
		}

		// 步骤切换按钮事件（展开/收起功能）
		if (stepToggle && listContainer) {
			stepToggle.addEventListener('click', () => {
				// 切换步骤展开/收起状态
				const isCollapsed = stepToggle.classList.contains('collapsed');

				if (isCollapsed) {
					// 展开
					stepToggle.classList.remove('collapsed');
					stepToggle.textContent = '▼';
					listContainer.style.display = 'block';
				} else {
					// 收起
					stepToggle.classList.add('collapsed');
					stepToggle.textContent = '▶';
					listContainer.style.display = 'none';
				}
			});
		}

		// 工具栏按钮事件
		this.addToolbarEventListeners(doc);

		// 添加窗口拖动功能
		this.addDragFunctionality(doc);
	}

	/**
	 * 显示空状态
	 */
	private showEmptyState(): void {
		if (!this.recordWindow) {
			return;
		}

		const container = this.recordWindow.document.getElementById('operationListContainer');
		if (container) {
			// 清空容器
			while (container.firstChild) {
				container.removeChild(container.firstChild);
			}

			// 创建空状态元素
			const emptyState = this.recordWindow.document.createElement('div');
			emptyState.className = 'empty-state';

			const title = this.recordWindow.document.createElement('div');
			title.textContent = '记录的操作';
			emptyState.appendChild(title);

			const subtitle = this.recordWindow.document.createElement('div');
			subtitle.style.marginTop = '8px';
			subtitle.style.fontSize = '11px';
			subtitle.textContent = '开始操作后，操作记录将显示在这里';
			emptyState.appendChild(subtitle);

			container.appendChild(emptyState);
		}
	}

	/**
	 * 显示录制状态提示
	 */
	private showRecordingStatus(status: 'starting' | 'recording' | 'paused'): void {
		if (!this.recordWindow) {
			return;
		}

		const container = this.recordWindow.document.getElementById('operationListContainer');
		if (container) {
			// 清空容器
			while (container.firstChild) {
				container.removeChild(container.firstChild);
			}

			// 创建状态提示元素
			const statusElement = this.recordWindow.document.createElement('div');
			statusElement.className = 'recording-status';
			statusElement.style.cssText = `
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 40px 20px;
				text-align: center;
				min-height: 200px;
			`;

			// 状态图标
			const iconElement = this.recordWindow.document.createElement('div');
			iconElement.style.cssText = `
				font-size: 48px;
				margin-bottom: 16px;
				color: #007ACC;
			`;

			// 状态文本
			const textElement = this.recordWindow.document.createElement('div');
			textElement.style.cssText = `
				font-size: 16px;
				font-weight: 600;
				color: #333;
				margin-bottom: 8px;
			`;

			// 副标题文本
			const subtitleElement = this.recordWindow.document.createElement('div');
			subtitleElement.style.cssText = `
				font-size: 12px;
				color: #666;
				line-height: 1.4;
			`;

			// 根据状态设置不同的内容
			switch (status) {
				case 'starting':
					iconElement.textContent = '⏳';
					textElement.textContent = '录制服务启动中...请等待';
					subtitleElement.textContent = '正在初始化录制环境，请稍候';
					break;
				case 'recording':
					iconElement.textContent = '🎯';
					textElement.textContent = '请选取高亮控件进行操作';
					subtitleElement.textContent = '录制已开始，点击界面上的控件进行操作录制';
					break;
				case 'paused':
					iconElement.textContent = '⏸️';
					textElement.textContent = '录制已暂停';
					subtitleElement.textContent = '点击继续按钮恢复录制';
					break;
			}

			// 添加加载动画（仅在启动状态）
			if (status === 'starting') {
				const loadingElement = this.recordWindow.document.createElement('div');
				loadingElement.style.cssText = `
					margin-top: 16px;
					display: inline-block;
					width: 20px;
					height: 20px;
					border: 3px solid #f3f3f3;
					border-top: 3px solid #007ACC;
					border-radius: 50%;
					animation: spin 1s linear infinite;
				`;

				// 添加CSS动画
				const style = this.recordWindow.document.createElement('style');
				style.textContent = `
					@keyframes spin {
						0% { transform: rotate(0deg); }
						100% { transform: rotate(360deg); }
					}
				`;
				this.recordWindow.document.head.appendChild(style);

				statusElement.appendChild(loadingElement);
			}

			statusElement.appendChild(iconElement);
			statusElement.appendChild(textElement);
			statusElement.appendChild(subtitleElement);
			container.appendChild(statusElement);
		}
	}

	/**
	 * 更新操作列表（性能优化版本）
	 */
	private updateOperationList(): void {
		if (!this.recordWindow) {
			return;
		}

		const container = this.recordWindow.document.getElementById('operationListContainer');
		if (!container) {
			return;
		}

		if (this.operations.length === 0) {
			this.showEmptyState();
			return;
		}

		// 使用增量更新策略
		this.performIncrementalUpdate(container);
	}

	/**
	 * 执行增量更新（性能优化）
	 */
	private performIncrementalUpdate(container: HTMLElement): void {
		// 按步骤分组操作记录
		const operationsByStep = this.groupOperationsByStep();

		// 简化逻辑：总是使用完全重建，但保留性能优化的调度机制
		// 这样确保界面正确显示，同时保持批量更新的性能优势
		this.performFullRebuild(container, operationsByStep);
	}

	/**
	 * 执行完全重建
	 */
	private performFullRebuild(container: HTMLElement, operationsByStep: Map<number, any>): void {
		// 清空容器
		while (container.firstChild) {
			container.removeChild(container.firstChild);
		}

		// 为每个步骤创建分组显示
		operationsByStep.forEach((stepGroup, stepIndex) => {
			const stepContainer = this.createStepContainer(stepGroup, stepIndex);
			container.appendChild(stepContainer);
		});

		// 滚动到底部
		this.scheduleScrollToBottom(container);
	}



	/**
	 * 调度滚动到底部（使用requestAnimationFrame优化）
	 */
	private scheduleScrollToBottom(container: HTMLElement): void {
		if (this.recordWindow) {
			this.recordWindow.requestAnimationFrame(() => {
				container.scrollTop = container.scrollHeight;
			});
		}
	}

	/**
	 * 按步骤分组操作记录
	 */
	private groupOperationsByStep(): Map<number, { stepName: string; operations: { operation: OperationRecord; index: number }[] }> {
		const groups = new Map<number, { stepName: string; operations: { operation: OperationRecord; index: number }[] }>();

		this.operations.forEach((operation, index) => {
			const stepIndex = operation.stepIndex ?? 0;

			// 改进步骤名称获取逻辑
			let stepName = operation.stepName;

			if (!stepName) {
				// 首先尝试从yamlSteps获取
				stepName = this.yamlSteps[stepIndex];

				// 如果yamlSteps中没有，尝试从当前测试用例的步骤信息获取
				if (!stepName && this.currentTestCase) {
					const steps = this.currentTestCase.steps || this.currentTestCase.TestCaseSteps || this.currentTestCase.testCaseSteps || [];
					if (steps[stepIndex]) {
						const step = steps[stepIndex];
						if (typeof step === 'string') {
							stepName = `步骤${stepIndex + 1}：${step}`;
						} else if (step && typeof step === 'object') {
							const desc = step.desc || step.description || step.name || step.title || step.value || '';
							if (desc && desc.trim()) {
								stepName = `步骤${step.index || stepIndex + 1}：${desc.trim()}`;
							}
						}
					}
				}

				// 最后的备选方案
				if (!stepName) {
					stepName = `步骤${stepIndex + 1}`;
				}
			}

			if (!groups.has(stepIndex)) {
				groups.set(stepIndex, {
					stepName,
					operations: []
				});
			}

			groups.get(stepIndex)!.operations.push({ operation, index });
		});

		return groups;
	}

	/**
	 * 创建步骤容器
	 */
	private createStepContainer(stepGroup: { stepName: string; operations: { operation: OperationRecord; index: number }[] }, stepIndex: number): HTMLElement {
		const stepContainer = this.recordWindow!.document.createElement('div');
		stepContainer.className = 'step-container';

		// 创建步骤标题
		const stepHeader = this.recordWindow!.document.createElement('div');
		stepHeader.className = 'step-header';

		// 折叠/展开图标
		const toggleIcon = this.recordWindow!.document.createElement('span');
		toggleIcon.className = 'step-toggle-icon';
		toggleIcon.textContent = '▼'; // 默认展开状态

		// 步骤标题文本
		const stepTitle = this.recordWindow!.document.createElement('span');
		stepTitle.className = 'step-title';
		stepTitle.textContent = stepGroup.stepName;

		// 操作数量
		const operationCount = this.recordWindow!.document.createElement('span');
		operationCount.className = 'operation-count';
		operationCount.textContent = `(${stepGroup.operations.length})`;

		stepHeader.appendChild(toggleIcon);
		stepHeader.appendChild(stepTitle);
		stepHeader.appendChild(operationCount);

		// 创建操作列表容器
		const operationsContainer = this.recordWindow!.document.createElement('div');
		operationsContainer.className = 'step-operations';

		// 添加操作记录
		stepGroup.operations.forEach(({ operation, index }) => {
			const item = this.createOperationItem(operation, index);
			operationsContainer.appendChild(item);
		});

		// 添加折叠/展开功能
		stepHeader.addEventListener('click', () => {
			const isCollapsed = operationsContainer.style.display === 'none';
			if (isCollapsed) {
				operationsContainer.style.display = 'block';
				toggleIcon.textContent = '▼';
				stepContainer.classList.remove('collapsed');
			} else {
				operationsContainer.style.display = 'none';
				toggleIcon.textContent = '▶';
				stepContainer.classList.add('collapsed');
			}
		});

		stepContainer.appendChild(stepHeader);
		stepContainer.appendChild(operationsContainer);

		return stepContainer;
	}

	/**
	 * 创建操作记录项
	 */
	private createOperationItem(operation: OperationRecord, index: number): HTMLElement {
		const item = this.recordWindow!.document.createElement('div');
		item.className = 'operation-item';

		// 为录制完成事件添加特殊样式
		if (operation.action === '录制完成' || operation.details?.isRecordingComplete) {
			item.classList.add('recording-complete');
		}

		// 为录制统计事件添加特殊样式
		if (operation.action === '录制统计' || operation.details?.isRecordingResult) {
			item.classList.add('recording-result');
		}

		// 为录制开始事件添加特殊样式
		if (operation.action === '录制开始' || operation.details?.isRecordingStart) {
			item.classList.add('recording-start');
		}

		// 为没有绑定UNI控件对象的鼠标事件添加红色醒目样式
		if (this.isMouseEventWithoutWidget(operation)) {
			item.classList.add('mouse-event-no-widget');
		}

		item.setAttribute('data-index', index.toString());

		const header = this.recordWindow!.document.createElement('div');
		header.className = 'operation-header';

		// 左侧：操作类型
		const type = this.recordWindow!.document.createElement('div');
		type.className = 'operation-type';
		type.textContent = this.getOperationTypeText(operation.type, operation);

		// 中间：时间信息
		const time = this.recordWindow!.document.createElement('div');
		time.className = 'operation-time';
		time.textContent = this.formatTime(operation.timestamp);

		// 右侧：删除按钮
		const deleteBtn = this.recordWindow!.document.createElement('button');
		deleteBtn.className = 'operation-delete-btn';
		deleteBtn.textContent = '×';
		deleteBtn.title = '删除此操作记录';
		deleteBtn.addEventListener('click', (e) => {
			e.stopPropagation();
			this.deleteOperation(index);
		});

		header.appendChild(type);
		header.appendChild(time);
		header.appendChild(deleteBtn);

		const action = this.recordWindow!.document.createElement('div');
		action.className = 'operation-action';
		action.textContent = operation.action;

		const target = this.recordWindow!.document.createElement('div');
		target.className = 'operation-target';
		target.textContent = this.formatTarget(operation.target);

		item.appendChild(header);
		item.appendChild(action);
		item.appendChild(target);

		return item;
	}

	/**
	 * 删除操作记录
	 */
	private deleteOperation(index: number): void {
		if (index >= 0 && index < this.operations.length) {
			// 从数组中删除操作记录
			this.operations.splice(index, 1);

			// 更新操作列表显示
			this.updateOperationList();

			// 可选：触发删除事件通知其他组件
			this.logService.info(`删除了第 ${index + 1} 个操作记录`);
		}
	}

	/**
	 * 清空所有操作记录
	 */
	private clearAllOperations(): void {
		if (this.operations.length === 0) {
			return;
		}

		// 确认对话框
		if (!this.recordWindow) {
			return;
		}

		const confirmed = this.recordWindow.confirm('确定要清空所有操作记录吗？此操作不可撤销。');
		if (confirmed) {
			// 清空操作数组
			this.operations = [];

			// 更新操作列表显示
			this.updateOperationList();

			// 记录日志
			this.logService.info('已清空所有操作记录');

			// 显示临时消息
			this.showTemporaryMessage('已清空所有操作记录');
		}
	}

	/**
	 * 判断是否为没有绑定UNI控件对象的鼠标事件
	 */
	private isMouseEventWithoutWidget(operation: OperationRecord): boolean {
		// 检查是否为鼠标事件类型
		const mouseEventTypes = ['mouse', 'mouse_click', 'mouse_right_click', 'mouse_double_click', 'mouse_drag', 'mouse_hover'];
		if (!mouseEventTypes.includes(operation.type)) {
			return false;
		}

		// 检查是否没有有效的控件信息
		// 如果widget_info为空、null、undefined，或者包含错误信息，则认为没有绑定控件
		if (!operation.widget_info) {
			return true;
		}

		// 检查widget_info是否包含错误信息
		if (operation.widget_info.error || operation.widget_info.capture_status === 'error') {
			return true;
		}

		// 检查是否为无效的控件信息（例如只有坐标信息但没有实际控件数据）
		if (operation.widget_info.RecordPosition &&
			(!operation.widget_info.Name || operation.widget_info.Name === '未知控件' || operation.widget_info.Name === 'N/A')) {
			return true;
		}

		return false;
	}

	/**
	 * 获取操作类型文本
	 */
	private getOperationTypeText(type: string, operation?: OperationRecord): string {
		switch (type) {
			case 'mouse':
				return '鼠标操作';
			case 'mouse_click':
				return '鼠标点击';
			case 'mouse_right_click':
				return '鼠标右击';
			case 'mouse_double_click':
				return '鼠标双击';
			case 'mouse_drag':
				return '鼠标拖动';
			case 'mouse_hover':
				return '鼠标悬停';
			case 'keyboard':
				return '键盘操作';
			case 'window':
				// 特殊处理录制完成事件
				if (operation?.action === '录制完成') {
					return '录制完成';
				}
				// 特殊处理录制统计事件
				if (operation?.action === '录制统计') {
					return '录制统计';
				}
				// 特殊处理录制开始事件
				if (operation?.action === '录制开始') {
					return '录制开始';
				}
				return '窗口操作';
			case 'menu':
				return '菜单操作';
			default:
				return '未知操作';
		}
	}

	/**
	 * 格式化时间
	 */
	private formatTime(timestamp: number): string {
		const date = new Date(timestamp);
		return date.toLocaleTimeString();
	}

	/**
	 * 格式化目标信息
	 */
	private formatTarget(target: any): string {
		if (target.name) {
			return `目标: ${target.name}`;
		}
		if (target.position) {
			return `位置: (${target.position.x}, ${target.position.y})`;
		}
		if (target.text) {
			return `文本: ${target.text}`;
		}
		return '未知目标';
	}

	/**
	 * 添加工具栏事件监听器
	 */
	private addToolbarEventListeners(doc: Document): void {
		// 暂停按钮
		const pauseBtn = doc.getElementById('pauseBtn');
		if (pauseBtn) {
			pauseBtn.addEventListener('click', () => {
				this.togglePause(true);
				window.postMessage({ type: 'gat:pauseRecording' }, '*');
			});
		}

		// 继续按钮
		const playBtn = doc.getElementById('playBtn');
		if (playBtn) {
			playBtn.addEventListener('click', () => {
				this.togglePause(false);
				window.postMessage({ type: 'gat:resumeRecording' }, '*');
			});
		}

		// 插入公共方法按钮
		const insertBtn = doc.getElementById('insertBtn');
		if (insertBtn) {
			insertBtn.addEventListener('click', () => {
				// 检查是否处于暂停状态
				const toolbar = doc.getElementById('toolbar');
				if (toolbar?.classList.contains('paused')) {
					// 显示提示信息
					this.showTemporaryMessage('暂停阶段不能打开插入公共方法窗口');
					return;
				}

				// 发送录制模式下的插入公共方法请求，包含当前选中的步骤信息
				const stepSelect = doc.getElementById('stepSelect') as HTMLSelectElement;
				const selectedStepIndex = stepSelect ? parseInt(stepSelect.value) : this.currentStepIndex;

				// 改进步骤名称获取逻辑
				let selectedStepName = this.yamlSteps[selectedStepIndex];

				if (!selectedStepName && this.currentTestCase) {
					const steps = this.currentTestCase.steps || this.currentTestCase.TestCaseSteps || this.currentTestCase.testCaseSteps || [];
					if (steps[selectedStepIndex]) {
						const step = steps[selectedStepIndex];
						if (typeof step === 'string') {
							selectedStepName = `步骤${selectedStepIndex + 1}：${step}`;
						} else if (step && typeof step === 'object') {
							const desc = step.desc || step.description || step.name || step.title || step.value || '';
							if (desc && desc.trim()) {
								selectedStepName = `步骤${step.index || selectedStepIndex + 1}：${desc.trim()}`;
							}
						}
					}
				}

				if (!selectedStepName) {
					selectedStepName = `步骤${selectedStepIndex + 1}`;
				}

				window.postMessage({
					type: 'gat:insertCommonMethodInRecording',
					stepIndex: selectedStepIndex,
					stepName: selectedStepName
				}, '*');
			});
		}

		// 设置按钮
		const settingsBtn = doc.getElementById('settingsBtn');
		if (settingsBtn) {
			settingsBtn.addEventListener('click', () => {
				this.showSettingsModal(doc);
			});
		}

		// 全流程录制按钮
		const fullRecordBtn = doc.getElementById('fullRecordBtn');
		if (fullRecordBtn) {
			fullRecordBtn.addEventListener('click', () => {
				// 检查是否处于暂停状态
				const toolbar = doc.getElementById('toolbar');
				if (toolbar?.classList.contains('paused')) {
					// 显示提示信息
					this.showTemporaryMessage('录制暂停时无法切换全流程录制');
					return;
				}

				// 检查是否已经启动全流程录制
				if (this.isFullRecording) {
					this.showTemporaryMessage('全流程录制已启动，停止录制后可重新启用');
					return;
				}

				this.toggleFullRecording();
			});
		}

		// 停止按钮
		const stopBtn = doc.getElementById('stopBtn');
		if (stopBtn) {
			stopBtn.addEventListener('click', () => {
				window.postMessage({ type: 'gat:stopRecording' }, '*');
				this.hide();
			});
		}

		// 清空按钮
		const clearBtn = doc.getElementById('clearBtn');
		if (clearBtn) {
			clearBtn.addEventListener('click', () => {
				this.clearAllOperations();
			});
		}
	}

	/**
	 * 切换暂停状态
	 */
	private togglePause(isPaused: boolean): void {
		if (!this.recordWindow) {
			return;
		}

		const toolbar = this.recordWindow.document.getElementById('toolbar');
		const pauseBtn = this.recordWindow.document.getElementById('pauseBtn');
		const playBtn = this.recordWindow.document.getElementById('playBtn');
		const insertBtn = this.recordWindow.document.getElementById('insertBtn');
		const fullRecordBtn = this.recordWindow.document.getElementById('fullRecordBtn');
		const recordingText = this.recordWindow.document.getElementById('recordingText');

		if (!toolbar || !pauseBtn || !playBtn || !insertBtn || !recordingText) {
			return;
		}

		if (isPaused) {
			// 暂停状态
			toolbar.classList.add('paused');
			pauseBtn.style.display = 'none';
			playBtn.style.display = 'flex';
			insertBtn.classList.add('disabled');
			insertBtn.title = '录制暂停时无法插入公共方法';
			recordingText.textContent = '已暂停';

			// 禁用全流程录制按钮
			if (fullRecordBtn) {
				fullRecordBtn.classList.add('disabled');
				fullRecordBtn.title = '录制暂停时无法切换全流程录制';
			}
		} else {
			// 恢复状态
			toolbar.classList.remove('paused');
			pauseBtn.style.display = 'flex';
			playBtn.style.display = 'none';
			insertBtn.classList.remove('disabled');
			insertBtn.title = '插入公共方法';
			recordingText.textContent = '正在录制';

			// 启用全流程录制按钮
			if (fullRecordBtn) {
				fullRecordBtn.classList.remove('disabled');
				fullRecordBtn.title = '全流程录制';
			}
		}
	}

	/**
	 * 显示临时消息
	 */
	private showTemporaryMessage(message: string): void {
		if (!this.recordWindow) {
			return;
		}

		// 创建临时消息元素
		const messageEl = this.recordWindow.document.createElement('div');
		messageEl.style.cssText = `
			position: fixed;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			background-color: #ff3b30;
			color: white;
			padding: 8px 16px;
			border-radius: 4px;
			font-size: 12px;
			z-index: 1000;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
		`;
		messageEl.textContent = message;

		this.recordWindow.document.body.appendChild(messageEl);

		// 3秒后移除消息
		setTimeout(() => {
			if (messageEl.parentNode) {
				messageEl.parentNode.removeChild(messageEl);
			}
		}, 3000);
	}

	/**
	 * 添加窗口拖动功能 - 参考插入公共方法窗口的优秀实现
	 */
	private addDragFunctionality(doc: Document): void {
		const header = doc.querySelector('.window-header') as HTMLElement;
		if (!header || !this.recordWindow) {
			return;
		}

		let isDragging = false;
		let dragStartPosition = { x: 0, y: 0 };
		let windowPosition = { x: 0, y: 0 };

		// 添加拖动样式
		const style = doc.createElement('style');
		style.textContent = `
			.window-header.dragging {
				cursor: grabbing !important;
				user-select: none;
			}
		`;
		doc.head.appendChild(style);

		// 鼠标按下事件
		header.addEventListener('mousedown', (e: MouseEvent) => {
			// 只处理左键点击
			if (e.button !== 0) {
				return;
			}

			// 如果点击的是按钮，不进行拖动
			const target = e.target as HTMLElement;
			if (target.classList.contains('control-btn') || target.closest('.control-btn') ||
				target.classList.contains('toolbar-btn') || target.closest('.toolbar-btn')) {
				return;
			}

			header.classList.add('dragging');
			isDragging = true;

			// 记录初始位置
			dragStartPosition = { x: e.screenX, y: e.screenY };
			windowPosition = { x: this.recordWindow!.screenX, y: this.recordWindow!.screenY };

			// 防止文本选中
			e.preventDefault();

			this.logService.debug(`开始拖动窗口，起始位置: (${windowPosition.x}, ${windowPosition.y})`);
		});

		// 鼠标移动事件 - 采用插入公共方法窗口的简洁实现
		doc.addEventListener('mousemove', (e: MouseEvent) => {
			if (!isDragging || !this.recordWindow) {
				return;
			}

			// 计算新的窗口位置
			const deltaX = e.screenX - dragStartPosition.x;
			const deltaY = e.screenY - dragStartPosition.y;

			const newX = windowPosition.x + deltaX;
			const newY = windowPosition.y + deltaY;

			// 直接移动窗口
			this.recordWindow.moveTo(newX, newY);
		});

		// 鼠标释放事件
		const endDrag = () => {
			if (isDragging && header) {
				header.classList.remove('dragging');
				isDragging = false;
				this.logService.debug(`结束拖动窗口，最终位置: (${this.recordWindow?.screenX}, ${this.recordWindow?.screenY})`);
			}
		};

		doc.addEventListener('mouseup', endDrag);
		doc.addEventListener('mouseleave', endDrag);

		// 双击标题栏将窗口居中（额外功能）
		header.addEventListener('dblclick', () => {
			if (!this.recordWindow) {
				return;
			}

			const screenWidth = window.screen.width;
			const screenHeight = window.screen.height;
			const windowWidth = 400;
			const windowHeight = 500;
			const windowLeft = (screenWidth - windowWidth) / 2;
			const windowTop = (screenHeight - windowHeight) / 2;

			windowPosition = { x: windowLeft, y: windowTop };
			this.recordWindow.moveTo(windowLeft, windowTop);
			this.logService.debug(`窗口已居中: (${windowLeft}, ${windowTop})`);
		});
	}

	/**
	 * 销毁窗口
	 */
	/**
	 * 创建设置对话框
	 */
	private createSettingsModal(doc: Document, container: HTMLElement): void {
		// 创建设置对话框
		const settingsModal = doc.createElement('div');
		settingsModal.className = 'settings-modal';
		settingsModal.id = 'settingsModal';
		container.appendChild(settingsModal);

		const settingsContent = doc.createElement('div');
		settingsContent.className = 'settings-content';
		settingsModal.appendChild(settingsContent);

		// 设置对话框头部
		const settingsHeader = doc.createElement('div');
		settingsHeader.className = 'settings-header';
		settingsContent.appendChild(settingsHeader);

		const title = doc.createElement('h3');
		title.textContent = '录制设置';
		settingsHeader.appendChild(title);

		const closeButton = doc.createElement('button');
		closeButton.className = 'close-button';
		closeButton.textContent = '×';
		closeButton.title = '关闭';
		settingsHeader.appendChild(closeButton);

		// 设置对话框主体
		const settingsBody = doc.createElement('div');
		settingsBody.className = 'settings-body';
		settingsContent.appendChild(settingsBody);

		const settingItem = doc.createElement('div');
		settingItem.className = 'setting-item';
		settingsBody.appendChild(settingItem);

		const label = doc.createElement('label');
		label.textContent = '全流程录制时长（秒）:';
		label.setAttribute('for', 'recordingDuration');
		settingItem.appendChild(label);

		const input = doc.createElement('input');
		input.type = 'number';
		input.id = 'recordingDuration';
		input.min = '10';
		input.max = '3600';
		input.value = '300';
		settingItem.appendChild(input);

		const description = doc.createElement('span');
		description.className = 'setting-description';
		description.textContent = '设置全流程录制的最大时长，默认300秒（5分钟）';
		settingItem.appendChild(description);

		// 设置对话框底部
		const settingsFooter = doc.createElement('div');
		settingsFooter.className = 'settings-footer';
		settingsContent.appendChild(settingsFooter);

		const cancelButton = doc.createElement('button');
		cancelButton.className = 'settings-btn cancel';
		cancelButton.textContent = '取消';
		settingsFooter.appendChild(cancelButton);

		const saveButton = doc.createElement('button');
		saveButton.className = 'settings-btn save';
		saveButton.textContent = '保存';
		settingsFooter.appendChild(saveButton);

		// 添加事件监听器
		closeButton.addEventListener('click', () => this.hideSettingsModal(doc));
		cancelButton.addEventListener('click', () => this.hideSettingsModal(doc));
		saveButton.addEventListener('click', () => this.saveSettings(doc));

		// 点击模态框背景关闭
		settingsModal.addEventListener('click', (e) => {
			if (e.target === settingsModal) {
				this.hideSettingsModal(doc);
			}
		});
	}

	/**
	 * 显示设置对话框
	 */
	private showSettingsModal(doc: Document): void {
		const settingsModal = doc.getElementById('settingsModal');
		const recordingDurationInput = doc.getElementById('recordingDuration') as HTMLInputElement;

		if (settingsModal && recordingDurationInput) {
			// 加载当前设置
			const currentDuration = this.getStoredRecordingDuration();
			recordingDurationInput.value = currentDuration.toString();

			// 显示对话框
			settingsModal.style.display = 'flex';
		}
	}

	/**
	 * 隐藏设置对话框
	 */
	private hideSettingsModal(doc: Document): void {
		const settingsModal = doc.getElementById('settingsModal');
		if (settingsModal) {
			settingsModal.style.display = 'none';
		}
	}

	/**
	 * 保存设置
	 */
	private saveSettings(doc: Document): void {
		const recordingDurationInput = doc.getElementById('recordingDuration') as HTMLInputElement;

		if (recordingDurationInput) {
			const duration = parseInt(recordingDurationInput.value);

			if (isNaN(duration) || duration < 10 || duration > 3600) {
				this.showTemporaryMessage('录制时长必须在10-3600秒之间');
				return;
			}

			// 保存设置到localStorage
			this.storeRecordingDuration(duration);

			// 隐藏对话框
			this.hideSettingsModal(doc);

			// 显示保存成功消息
			this.showTemporaryMessage(`设置已保存：录制时长 ${duration} 秒`);
		}
	}

	/**
	 * 获取存储的录制时长
	 */
	private getStoredRecordingDuration(): number {
		try {
			const settings = localStorage.getItem('gat-recording-settings');
			if (settings) {
				const parsed = JSON.parse(settings);
				return parsed.recordingDuration || 300;
			}
		} catch (error) {
			console.warn('无法加载录制设置:', error);
		}
		return 300; // 默认300秒
	}

	/**
	 * 存储录制时长
	 */
	private storeRecordingDuration(duration: number): void {
		try {
			const settings = {
				recordingDuration: duration
			};
			localStorage.setItem('gat-recording-settings', JSON.stringify(settings));
		} catch (error) {
			console.error('无法保存录制设置:', error);
		}
	}

	/**
	 * 发送窗口位置信息给后端
	 */
	private sendWindowBoundsToBackend(): void {
		if (!this.recordWindow) {
			return;
		}

		try {
			// 获取窗口位置和大小信息
			const bounds = {
				x: this.recordWindow.screenX,
				y: this.recordWindow.screenY,
				width: this.recordWindow.outerWidth,
				height: this.recordWindow.outerHeight
			};

			// 通过主窗口发送消息给后端
			window.postMessage({
				type: 'gat:updateRecorderWindowBounds',
				bounds: bounds
			}, '*');

			this.logService.debug(`发送录制控制器窗口位置信息: x=${bounds.x}, y=${bounds.y}, width=${bounds.width}, height=${bounds.height}`);
		} catch (error) {
			this.logService.error(`发送窗口位置信息失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 设置窗口移动监听器
	 */
	private setupWindowMoveListener(): void {
		if (!this.recordWindow) {
			return;
		}

		// 监听窗口移动和大小变化事件
		let moveTimer: number | null = null;

		const handleWindowChange = () => {
			// 使用防抖机制，避免频繁发送位置更新
			if (moveTimer) {
				clearTimeout(moveTimer);
			}

			moveTimer = window.setTimeout(() => {
				this.sendWindowBoundsToBackend();
				moveTimer = null;
			}, 200); // 200ms 防抖延迟
		};

		// 监听窗口移动事件
		this.recordWindow.addEventListener('resize', handleWindowChange);

		// 由于没有直接的move事件，我们使用定时器定期检查位置变化
		let lastX = this.recordWindow.screenX;
		let lastY = this.recordWindow.screenY;

		const checkPosition = () => {
			if (!this.recordWindow) {
				return;
			}

			const currentX = this.recordWindow.screenX;
			const currentY = this.recordWindow.screenY;

			if (currentX !== lastX || currentY !== lastY) {
				lastX = currentX;
				lastY = currentY;
				handleWindowChange();
			}
		};

		// 每500ms检查一次位置变化
		const positionCheckInterval = setInterval(checkPosition, 500);

		// 在窗口关闭时清理定时器
		this.recordWindow.addEventListener('beforeunload', () => {
			if (moveTimer) {
				clearTimeout(moveTimer);
			}
			clearInterval(positionCheckInterval);
		});
	}

	override dispose(): void {
		this.hide();
		super.dispose();
	}
}
