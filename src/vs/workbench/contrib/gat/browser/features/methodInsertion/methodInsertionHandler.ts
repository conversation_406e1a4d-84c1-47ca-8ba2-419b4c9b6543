/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2023-2024 <PERSON><PERSON><PERSON>. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { ILogService } from '../../../../../../platform/log/common/log.js';
import { INotificationService } from '../../../../../../platform/notification/common/notification.js';
import { IInstantiationService } from '../../../../../../platform/instantiation/common/instantiation.js';
import { IWorkspaceContextService } from '../../../../../../platform/workspace/common/workspace.js';
import { IFileService } from '../../../../../../platform/files/common/files.js';
import { IConfigurationService } from '../../../../../../platform/configuration/common/configuration.js';
import { URI } from '../../../../../../base/common/uri.js';
import { joinPath } from '../../../../../../base/common/resources.js';
import { VSBuffer } from '../../../../../../base/common/buffer.js';
import { GATPathResolver } from '../../common/pathResolver.js';
import { InsertMethodWindow } from '../methodWindow/index.js';
import { DisposableStore } from '../../../../../../base/common/lifecycle.js';
import { localize } from '../../../../../../nls.js';

/**
 * 方法插入处理器
 * 负责处理方法插入相关的功能
 */
export class MethodInsertionHandler {
    // 插入公共方法窗口
    private insertMethodWindow: InsertMethodWindow | null = null;

    // 标记方法数据是否已预加载
    private methodDataPreloaded = false;

    // 当前测试用例
    private currentTestCase: any | null = null;

    // 是否正在录制
    private isRecording = false;

    // 是否暂停
    private isPaused = false;

    // 录制模式相关状态
    private isRecordingMode = false;
    private recordingStepIndex = 0;
    private recordingStepName = '';

    // 用于存储订阅的事件处理器
    private readonly _disposables = new DisposableStore();

    // 路径解析器
    private readonly pathResolver: GATPathResolver;

    constructor(
        private readonly sendStatusMessage: (message: string) => void,
        @ILogService private readonly logService: ILogService,
        @INotificationService private readonly notificationService: INotificationService,
        @IInstantiationService private readonly instantiationService: IInstantiationService
    ) {
        // 初始化路径解析器
        const configService = this.instantiationService.invokeFunction(a => a.get(IConfigurationService));
        const workspaceService = this.instantiationService.invokeFunction(a => a.get(IWorkspaceContextService));
        const fileService = this.instantiationService.invokeFunction(a => a.get(IFileService));

        this.pathResolver = new GATPathResolver(
            configService,
            workspaceService,
            fileService,
            this.logService
        );
    }

    /**
     * 设置当前测试用例
     */
    public setCurrentTestCase(testCase: any): void {
        this.currentTestCase = testCase;
    }

    /**
     * 设置录制状态
     */
    public setRecordingState(isRecording: boolean, isPaused: boolean): void {
        this.isRecording = isRecording;
        this.isPaused = isPaused;
    }

    /**
     * 设置录制模式
     * @param isRecordingMode 是否为录制模式
     * @param stepIndex 目标步骤索引
     * @param stepName 目标步骤名称
     */
    public setRecordingMode(isRecordingMode: boolean, stepIndex?: number, stepName?: string): void {
        this.isRecordingMode = isRecordingMode;
        if (stepIndex !== undefined) {
            this.recordingStepIndex = stepIndex;
        }
        if (stepName !== undefined) {
            this.recordingStepName = stepName;
        }
        this.logService.info(`设置录制模式: ${isRecordingMode}, 步骤: ${stepName} (索引: ${stepIndex})`);
    }

    /**
     * 处理录制模式下的方法插入
     * @param methodName 方法名
     * @param formattedCode 格式化的YAML代码
     */
    private handleRecordingModeInsertion(methodName: string, formattedCode: string): void {
        // 发送消息给TestCaseRecorder，让它将方法节点添加到操作记录窗口
        window.postMessage({
            type: 'gat:addCommonMethodNode',
            methodName: methodName,
            formattedCode: formattedCode,
            stepIndex: this.recordingStepIndex,
            stepName: this.recordingStepName
        }, '*');

        // 发送状态信息
        this.sendStatusMessage(`已将方法 ${methodName} 添加到录制列表`);

        // 重置录制模式状态
        this.isRecordingMode = false;
        this.recordingStepIndex = 0;
        this.recordingStepName = '';

        this.logService.info(`录制模式插入完成: ${methodName}`);
    }

    /**
     * 预加载方法数据
     * 在录制开始时预加载方法数据，提高插入公共方法窗口的响应速度
     * 优化版本：使用非阻塞方式预加载，不影响录制工具条的显示
     */
    public preloadMethodData(): void {
        if (this.methodDataPreloaded) {
            return;
        }

        // 使用非阻塞方式预加载，不影响录制工具条的显示
        setTimeout(async () => {
            try {
                // 创建插入公共方法窗口对象，但不创建实际窗口
                if (!this.insertMethodWindow) {
                    this.logService.info('预加载方法数据 - 开始创建插入公共方法窗口');
                    // 使用实例化服务创建窗口对象
                    this.insertMethodWindow = this.instantiationService.createInstance(InsertMethodWindow);

                    // 监听方法插入事件
                    this._disposables.add(this.insertMethodWindow!.onMethodInserted(({ methodName, parameters, testCaseContext, isEdit, originalYAML, testCaseFileUri }: { methodName: string; parameters: Record<string, any>; testCaseContext: any; isEdit: boolean; originalYAML?: string; testCaseFileUri?: string }) => {
                        this.logService.info('使用 onMethodInserted 接收到的 testCaseContext:', JSON.stringify(testCaseContext, null, 2));
                        this.logService.info(`已插入方法: ${methodName}，参数:`, parameters);

                        // 获取格式化的YAML代码
                        const formattedCode = this.insertMethodWindow!.eventHandler.getFormattedParameters() || '';

                        // 记录格式化的YAML代码
                        this.logService.info(`格式化的YAML代码:\n${formattedCode}`);

                        // 实际插入方法到测试用例，使用选中节点
                        const section = this.insertMethodWindow!.getSelectedSection();
                        // 记录选中节点，便于调试
                        this.logService.info(`使用的目标步骤节点: ${section}`);



                        if (isEdit && originalYAML) {
                            this.editMethodInTestCase(methodName, originalYAML, formattedCode, section, testCaseContext, testCaseFileUri);
                        } 
			else if (this.isRecordingMode) {
                            // 录制模式：将方法节点添加到录制窗口，不直接写入文件
                            this.logService.info(`录制模式下插入方法: ${methodName} 到步骤 ${this.recordingStepName}`);
                            this.handleRecordingModeInsertion(methodName, formattedCode);
                        } else {
                            // 普通模式：直接插入到测试用例文件
                            const section = this.insertMethodWindow!.getSelectedSection();
                            this.logService.info(`使用的目标步骤节点: ${section}`);
                            if (isEdit && originalYAML) {
                                this.editMethodInTestCase(methodName, originalYAML, formattedCode, section, testCaseContext, testCaseFileUri);
                            } else {
                                this.insertMethodToTestCase(methodName, formattedCode, section, testCaseContext);
                            }
                            // 发送状态信息到录制工具条
                            this.sendStatusMessage(`已插入方法: ${methodName}`);
                        }
                    }));

                    // 监听捕获控件消息事件
                    this._disposables.add(this.insertMethodWindow!.onCaptureControl(message => {
                        this.logService.info(`接收到捕获控件消息: ${message}`);
                        // 发送状态信息到录制工具条
                        this.sendStatusMessage(message);
                    }));

                    // 只预加载方法数据，不创建窗口
                    await this.insertMethodWindow!.preloadMethodData();
                    this.methodDataPreloaded = true;
                    this.logService.info('方法数据预加载完成');
                }
            } catch (error) {
                this.logService.error('预加载方法数据失败:', error);
                // 预加载失败不影响正常使用，只是会导致首次打开窗口时有延迟
            }
        }, 500); // 增加延迟，确保录制工具条先显示
    }

    /**
     * 插入公共方法
     * 优化版本：利用预加载的窗口和数据，减少延迟
     */
    public async insertCommonMethod(): Promise<void> {
        try {
            // 设置消息
            this.sendStatusMessage('正在打开插入公共方法窗口...');
            this.logService.info('插入公共方法 - 当前上下文:', JSON.stringify({
                testCaseId: this.currentTestCase?.TestCaseID || this.currentTestCase?.id,
                hasTestCaseAppList: !!this.currentTestCase?.TestCaseAppList
            }));

            if (!this.isRecording) {
                return;
            }

            // 检查是否处于暂停状态（录制模式下允许在暂停状态下插入）
            if (this.isPaused && !this.isRecordingMode) {
                this.logService.info('录制已暂停，无法插入公共方法');
                // 发送更明确的提示信息
                this.sendStatusMessage('录制暂停期间无法打开插入公共方法界面');
                return;
            }

            if (!this.currentTestCase) {
                this.notificationService.error(localize('selectTestCaseFirst', "请先选择一个测试用例"));
                return;
            }

            this.logService.info(`插入公共方法到测试用例: ${this.currentTestCase.TestCaseID || this.currentTestCase.id}`);

            // 发送状态信息
            this.sendStatusMessage('正在打开插入公共方法窗口...');

            // 创建插入公共方法窗口
            if (!this.insertMethodWindow) {
                this.insertMethodWindow = this.instantiationService.createInstance(InsertMethodWindow);
                // 同步监听方法插入事件，基于选中节点插入代码
                this.insertMethodWindow.onMethodInserted(({ methodName, parameters, testCaseContext, isEdit, originalYAML, testCaseFileUri }: { methodName: string; parameters: Record<string, any>; testCaseContext: any; isEdit: boolean; originalYAML?: string; testCaseFileUri?: string }) => {
                    this.logService.info('使用 UI 触发 onMethodInserted 接收到的 testCaseContext:', JSON.stringify(testCaseContext, null, 2));
                    // 获取格式化的YAML代码
                    const formattedCode = this.insertMethodWindow!.eventHandler.getFormattedParameters() || '';
                    const section = this.insertMethodWindow!.getSelectedSection();
                    this.logService.info(`使用的目标步骤节点(UI插入): ${section}`);
                    if (isEdit && originalYAML) {
                        this.editMethodInTestCase(methodName, originalYAML, formattedCode, section, testCaseContext, testCaseFileUri);
                    } else {
                        this.insertMethodToTestCase(methodName, formattedCode, section, testCaseContext);
                    }
                    // 发送状态信息
                    this.sendStatusMessage(`已将方法 ${methodName} 插入到 ${section}`);
                });
                // 监听捕获控件消息事件
                this.insertMethodWindow.onCaptureControl(message => {
                    this.logService.info(`接收到捕获控件消息: ${message}`);
                    this.sendStatusMessage(message);
                });
            }

            // 显示窗口
            try {
                // 调试：打印 currentTestCase，查看是否含 TestCaseAppList
                this.logService.info('调试: currentTestCase=', JSON.stringify(this.currentTestCase));
                // 注入测试用例上下文和应用对象列表
                this.insertMethodWindow.setTestCaseContext(this.currentTestCase);
                this.logService.info('准备加载应用列表 - 调用listApplications()');
                const apps = await this.listApplications();
                this.logService.info('调试: listApplications结果=', JSON.stringify(apps));
                this.insertMethodWindow.setAppList(apps);
                // 启用步骤选择器（仅在录制工具条上下文调用时）
                this.insertMethodWindow.stepSelectorEnabled = true;
                await this.insertMethodWindow.show();
                this.logService.info('插入公共方法窗口显示成功');
            } catch (error) {
                this.logService.error(`显示公共方法窗口失败: ${error instanceof Error ? error.message : String(error)}`);
                this.notificationService.error(localize('showMethodWindowError', "显示公共方法窗口失败: {0}", error instanceof Error ? error.message : String(error)));
            }
        } catch (error) {
            this.logService.error(`插入公共方法错误: ${error instanceof Error ? error.message : String(error)}`);
            this.notificationService.error(localize('insertMethodError', "插入公共方法失败: {0}", error instanceof Error ? error.message : String(error)));
        }
    }

    /**
     * 向测试用例文件插入方法
     * 使用直接文本处理方式而非YAML解析器，以保持原始格式和特殊字符
     * @param targetSection 目标节点文本（如 'setup'、'teardown'、'steps' 或 '步骤1：描述'）
     * @param testCaseContextParam 可选，测试用例上下文
     */
    private async insertMethodToTestCase(methodName: string, formattedCode: string, targetSection: string, testCaseContextParam?: any): Promise<void> {
        const tc = testCaseContextParam || this.currentTestCase;
        if (!tc) {
            this.logService.warn('没有当前测试用例，无法插入方法');
            return;
        }

        try {
            const testCaseId = tc.id;
            this.logService.info(`准备将方法 ${methodName} 插入到测试用例 ${testCaseId} 的 ${targetSection} 部分 (使用行处理方式)`);

            // 获取所需服务
            const fileService = this.instantiationService.invokeFunction(accessor => accessor.get(IFileService));
            const configService = this.instantiationService.invokeFunction(accessor => accessor.get(IConfigurationService));
            const workspaceService = this.instantiationService.invokeFunction(accessor => accessor.get(IWorkspaceContextService));

            // 使用路径解析器确定测试用例目录 URI
            const pathResolver = new GATPathResolver(configService, workspaceService, fileService, this.logService);
            const testcaseUri = await pathResolver.getTestcasePath();
            if (!testcaseUri) {
                throw new Error('无法获取有效的测试用例路径');
            }
            this.logService.info(`测试用例根目录: ${testcaseUri.toString()}`);

            // 查找测试用例文件
            const ymlFileName = `${testCaseId}.yml`;
            const yamlFileName = `${testCaseId}.yaml`;
            const findFiles = async (dir: URI, fileName: string): Promise<URI[]> => {
                const result: URI[] = [];
                try {
                    const entries = await fileService.resolve(dir, { resolveMetadata: false });
                    if (!entries.children) { return result; }
                    for (const entry of entries.children) {
                        if (entry.isDirectory) {
                            result.push(...await findFiles(entry.resource, fileName));
                        } else if (entry.name === fileName) {
                            result.push(entry.resource);
                        }
                    }
                } catch (error) {
                    if (error instanceof Error && error.name === 'NotFoundError') {
                        this.logService.warn(`查找文件时目录不存在: ${dir.toString()}`);
                    } else {
                        this.logService.warn(`查找文件时出错 ${dir.toString()}: ${error}`);
                    }
                }
                return result;
            };
            let files = await findFiles(testcaseUri, ymlFileName);
            if (files.length === 0) {
                files = await findFiles(testcaseUri, yamlFileName);
            }

            let testCaseFile: URI;
            let content = '';

            if (files.length > 0) {
                testCaseFile = files[0];
                this.logService.info(`找到测试用例文件: ${testCaseFile.toString()}`);
                try {
                    const fileContent = await fileService.readFile(testCaseFile);
                    content = fileContent.value.toString();
                } catch (readError) {
                    this.logService.error(`读取文件 ${testCaseFile.toString()} 出错: ${readError}`);
                    // 出错时也视为新文件处理，保留 content 为空字符串
                }
            } else {
                // 如果找不到文件，确定一个新文件路径
                const safeFileName = ymlFileName;
                testCaseFile = joinPath(testcaseUri, safeFileName);
                // 新文件内容为空
                this.logService.info(`未找到测试用例文件，将在 ${testCaseFile.toString()} 创建新文件`);
            }

            // --- 开始使用行处理方式 ---
            const lines = content.split('\n');

            // 确保基础结构存在（对于新文件）
            if (content.trim() === '') {
                // 新文件基础结构，使用上下文tc
                lines.push(`id: ${testCaseId}`);
                lines.push(`name: ${tc.name || testCaseId}`);
                if (tc.story) { lines.push(`story: ${tc.story}`); }
                if (tc.module) { lines.push(`module: ${tc.module}`); }
                if (tc.priority) { lines.push(`priority: ${tc.priority}`); }
                if (tc.execPriority) { lines.push(`execPriority: ${tc.execPriority}`); }
                if (tc.recordType) { lines.push(`recordType: ${tc.recordType}`); }
                if (typeof tc.directRecord !== 'undefined') { lines.push(`directRecord: ${tc.directRecord}`); }
                if (tc.author) { lines.push(`author: ${tc.author}`); }
                if (tc.testSetName) { lines.push(`testSetName: ${tc.testSetName}`); }
                if (tc.description) { lines.push(`description: ${tc.description}`); }
            }

            // 确定要插入的内容格式
            let stepContent = '';
            if (formattedCode && formattedCode.trim()) {
                // 修改格式化逻辑，直接使用formattedCode内容，而不是添加额外的action和params嵌套
                stepContent = formattedCode;
            } else {
                stepContent = `- action: ${methodName}`;
            }

            // 查找目标部分的范围
            const targetLineData = this.findTargetSectionRange(lines, targetSection);

            if (targetLineData.exists) {
                this.logService.info(`找到目标部分 ${targetSection}`);
                // 相对目标行缩进，根据 section 类型使用不同额外空格
                const indent = (targetLineData as any).indent || 0;
                const codeLines = stepContent.split('\n');
                const prefixLen = codeLines[0].search(/\S/);
                const extraIndent = targetSection.startsWith('步骤') ? 4 : 2; // 步骤使用+4，否则+2
                const toInsert = codeLines.map(line =>
                    ' '.repeat(indent + extraIndent) +
                    (line.startsWith(' '.repeat(prefixLen)) ? line.slice(prefixLen) : line)
                );
                lines.splice(targetLineData.insertLineIndex, 0, ...toInsert);
                // 若上一行含 '~' 或 'null' 则恢复
                const prevIndex = targetLineData.insertLineIndex - 1;
                if (lines[prevIndex] && (lines[prevIndex].includes('~') || lines[prevIndex].includes('null'))) {
                    this.logService.info(`移除目标行 ${prevIndex} 的 '~' 或 'null'`);
                    lines[prevIndex] = lines[prevIndex].replace(/:\s*(~|null)$/, ':');
                }
            } else if (targetSection.startsWith('步骤')) {
                // 如果是步骤列表，且该步骤标签不存在，插入新的子节点到 steps 下
                this.logService.info(`未找到步骤节点 ${targetSection}，将在 steps 下创建`);
                const stepsRange = this.findTargetSectionRange(lines, 'steps');
                if (stepsRange.exists) {
                    // 构造缩进为4空格的步骤行及内部action行（6空格缩进），先 trim 子行
                    const indentBase = stepsRange.indent || 0;
                    const nestedLines = [
                        `${' '.repeat(indentBase + 4)}- ${targetSection}:`,
                        ...stepContent.trim().split('\n').map(line => `${' '.repeat(indentBase + 6)}${line.trim()}`)
                    ];
                    lines.splice(stepsRange.insertLineIndex, 0, ...nestedLines);
                } else {
                    // steps 不存在则同时创建
                    lines.push(
                        '',
                        'steps:',
                        ...[
                            `${' '.repeat(4)}- ${targetSection}:`,
                            ...stepContent.trim().split('\n').map(line => `${' '.repeat(6)}${line.trim()}`)
                        ]
                    );
                }
            } else {
                // 如果目标部分不存在，添加到文件末尾
                this.logService.info(`未找到目标部分 ${targetSection}，将添加到文件末尾`);
                lines.push('', `${targetSection}:`, stepContent);
            }

            const newContent = lines.join('\n');

            // 将新内容写回文件
            await fileService.writeFile(testCaseFile, VSBuffer.fromString(newContent));
            this.logService.info(`已将更新后的内容写入文件: ${testCaseFile.toString()} (使用行处理方式)`);

            // 显示成功通知
            this.notificationService.info(localize('methodInserted', "已将方法 {0} 插入到测试用例 {1}", methodName, testCaseId));

        } catch (error) {
            this.logService.error(`使用行处理方式插入方法到测试用例时出错: ${error instanceof Error ? error.message : String(error)}`, error);
            this.notificationService.error(localize('methodInsertError', "插入方法到测试用例时出错: {0}",
                error instanceof Error ? error.message : String(error)));
        }
    }

    /**
     * 编辑测试用例中的方法
     * @param methodName 方法名
     * @param originalYAML 原始YAML代码
     * @param newFormattedCode 新的格式化代码
     * @param targetSection 目标部分
     * @param testCaseContextParam 测试用例上下文
     * @param testCaseFileUriStr 测试用例文件URI字符串
     */
    private async editMethodInTestCase(
        methodName: string,
        originalYAML: string,
        newFormattedCode: string,
        targetSection: string,
        testCaseContextParam?: any,
        testCaseFileUriStr?: string
    ): Promise<void> {
        const tc = testCaseContextParam || this.currentTestCase;
        if (!tc) { this.logService.warn('没有当前测试用例，无法编辑方法'); return; }
        try {
            const fileService = this.instantiationService.invokeFunction(a => a.get(IFileService));
            let fileUri: URI;
            if (testCaseFileUriStr) {
                // 编辑流程传入直接使用
                fileUri = URI.parse(testCaseFileUriStr);
            } else {
                // 使用路径解析器获取测试用例路径
                const testcaseUri = await this.pathResolver.getTestcasePath();
                if (!testcaseUri) {
                    throw new Error('无法获取有效的测试用例路径');
                }
                const rootUri = testcaseUri;
                const ymlName = `${tc.id}.yml`, yamlName = `${tc.id}.yaml`;
                // 递归查找
                async function findFiles(dir: URI, name: string): Promise<URI[]> {
                    const res: URI[] = [];
                    const entries = await fileService.resolve(dir, { resolveMetadata: false });
                    for (const e of entries.children || []) {
                        if (e.isDirectory) {
                            res.push(...await findFiles(e.resource, name));
                        } else if (e.name === name) {
                            res.push(e.resource);
                        }
                    }
                    return res;
                }
                let files = await findFiles(rootUri, ymlName);
                if (!files.length) { files = await findFiles(rootUri, yamlName); }
                if (files.length) {
                    fileUri = files[0];
                } else {
                    fileUri = joinPath(rootUri, ymlName);
                    this.logService.info(`未找到测试用例文件，将创建: ${fileUri.toString()}`);
                }
            }
            // 读取内容
            let content = '';
            try { content = (await fileService.readFile(fileUri)).value.toString(); } catch { /* 新文件空 */ }
            const lines = content.split('\n');
            // 定位旧 action block
            const prefix = `- action: ${methodName}`;
            const start = lines.findIndex(l => l.trim().startsWith(prefix));
            if (start < 0) { this.logService.warn(`未找到旧方法 ${methodName}`); return; }
            const indent = lines[start].search(/\S/);
            let end = start + 1;
            while (end < lines.length) {
                const l = lines[end];
                if (l.trim().startsWith('- action:') && l.search(/\S/) <= indent) { break; }
                end++;
            }
            // 构建新行
            const newLines = newFormattedCode.split('\n').map(l => ' '.repeat(indent) + l);
            lines.splice(start, end - start, ...newLines);
            // 写回文件
            await fileService.writeFile(fileUri, VSBuffer.fromString(lines.join('\n')));
            this.logService.info(`已替换 ${fileUri.toString()} 中方法 ${methodName}`);
        } catch (err) {
            this.logService.error('编辑方法失败:', err);
        }
    }

    /**
     * 查找目标部分的范围，以便在正确位置插入新内容
     * @param lines 文件内容的行数组
     * @param targetSection 目标部分（setup、teardown、steps）
     * @returns 包含目标部分信息的对象
     */
    private findTargetSectionRange(lines: string[], targetSection: string): {
        exists: boolean;
        insertLineIndex: number;
        indent?: number;
    } {
        if (targetSection.startsWith('步骤')) {
            // 兼容半角和全角冒号，提取步骤节点基名，如"步骤1"
            const sectionBase = targetSection.split(/[:：\uff1a]/)[0];
            // 定位 steps 区块范围
            let stepsStart = -1;
            for (let idx = 0; idx < lines.length; idx++) {
                const tl = lines[idx].trim();
                if (tl === 'steps:' || tl === 'steps：' || tl === 'steps\uff1a') { stepsStart = idx; break; }
            }
            if (stepsStart >= 0) {
                const baseIndent = lines[stepsStart].search(/\S/);
                let stepsEnd = lines.length;
                for (let idx = stepsStart + 1; idx < lines.length; idx++) {
                    const l = lines[idx];
                    if (l.trim() === '') continue;
                    const ind = l.search(/\S/);
                    if (ind <= baseIndent && l.trim().includes(':')) { stepsEnd = idx; break; }
                }
                // 在 steps 区块中查找目标自定义节点，支持半角或全角冒号
                for (let i = stepsStart + 1; i < stepsEnd; i++) {
                    const line = lines[i];
                    const trimmed = line.trim();
                    if (trimmed.startsWith(`- ${sectionBase}:`) || trimmed.startsWith(`- ${sectionBase}：`) || trimmed.startsWith(`- ${sectionBase}\uff1a`)) {
                        const indent = line.search(/\S/);
                        let insertLineIndex = i + 1;
                        for (let j = i + 1; j < stepsEnd; j++) {
                            const l = lines[j]; if (l.trim() === '') continue;
                            const ind = l.search(/\S/);
                            if (ind > indent) { insertLineIndex = j + 1; } else { break; }
                        }
                        return { exists: true, insertLineIndex, indent };
                    }
                }
            }
            // 未找到自定义节点，继续后续逻辑
        }
        let targetLineIndex = -1;
        let currentIndent = -1;
        let sectionEndLine = lines.length;

        // 查找目标部分的开始行
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const trimmedLine = line.trim();

            // 匹配目标部分（注意冒号可能是全角或半角）
            if (trimmedLine === `${targetSection}:` ||
                trimmedLine === `${targetSection}：` ||
                trimmedLine === `${targetSection}\uff1a` ||
                trimmedLine === `${targetSection}: ~` ||
                trimmedLine === `${targetSection}：~` ||
                trimmedLine === `${targetSection}\uff1a~` ||
                trimmedLine === `${targetSection}: null` ||
                trimmedLine === `${targetSection}：null` ||
                trimmedLine === `${targetSection}\uff1anull`) {
                targetLineIndex = i;
                currentIndent = line.search(/\S/);
                break;
            }

            // 支持嵌套节点格式，如 "- 步骤2：测试不是: ~"
            if (trimmedLine.includes(`${targetSection}:`) ||
                trimmedLine.includes(`${targetSection}：`) ||
                trimmedLine.includes(`${targetSection}\uff1a`)) {
                const matchPart = trimmedLine.split(':')[0].trim();
                if (matchPart.endsWith(targetSection)) {
                    targetLineIndex = i;
                    currentIndent = line.search(/\S/);
                    break;
                }
            }
        }

        // 如果找到目标部分，确定其范围
        if (targetLineIndex !== -1) {
            // 确定部分结束的行号（找到下一个同级或更高级的部分）
            for (let i = targetLineIndex + 1; i < lines.length; i++) {
                const line = lines[i];
                if (line.trim() === '') continue; // 跳过空行

                const indent = line.search(/\S/);
                // 如果缩进减少或等于当前部分，认为当前部分结束
                if (indent <= currentIndent && line.trim().includes(':')) {
                    sectionEndLine = i;
                    break;
                }
            }

            // 确定插入位置
            let insertLineIndex = targetLineIndex + 1; // 默认为目标行的下一行
            // 检查目标部分的下一行是否还是属于该部分 (缩进更大)
            if (targetLineIndex + 1 < lines.length &&
                lines[targetLineIndex + 1].trim() !== '' &&
                lines[targetLineIndex + 1].search(/\S/) > currentIndent) {
                // 如果有子元素，则使用计算出的 sectionEndLine 作为插入点
                insertLineIndex = sectionEndLine;
            }
            // 如果目标行包含 '~' 或 'null'，表示为空列表，强制插入在下一行
            if (lines[targetLineIndex].includes('~') || lines[targetLineIndex].includes('null')) {
                insertLineIndex = targetLineIndex + 1;
            }

            return {
                exists: true,
                insertLineIndex: insertLineIndex,
                indent: currentIndent
            };
        }

        // 如果没有找到目标部分
        return {
            exists: false,
            insertLineIndex: lines.length // 在文件末尾插入
        };
    }

    /**
     * 获取应用对象列表，用于下拉显示
     * 总是从app_menu.py加载完整应用列表，如果有TestCaseAppList，用于标记默认选中项
     */
    private async listApplications(): Promise<Array<{ label: string, value: string, selected?: boolean }>> {
        this.logService.info('listApplications - 开始获取应用列表');

        // 从TestCaseAppList获取应用标识符列表（如果存在）
        const appList = this.currentTestCase?.TestCaseAppList;
        let appIdentifiers: string[] = [];

        if (appList) {
            this.logService.info('listApplications - 处理TestCaseAppList:',
                typeof appList === 'string' ? appList : JSON.stringify(appList));

            if (Array.isArray(appList)) {
                // 如果是数组，提取每一项
                appIdentifiers = appList.map(item => {
                    const itemStr = String(item);
                    // 从路径中提取basename
                    return itemStr.includes('/') ? itemStr.split('/').pop() || itemStr : itemStr;
                });
            } else if (typeof appList === 'string') {
                // 如果是字符串，按分号分割并提取每一项
                appIdentifiers = appList
                    .split(/;+?/)
                    .map(v => v.trim())
                    .filter(v => v)
                    .map(v => v.includes('/') ? v.split('/').pop() || v : v);
            }

            this.logService.info('listApplications - 提取的应用标识符:', JSON.stringify(appIdentifiers));
        }

        // 获取服务
        const configurationService = this.instantiationService.invokeFunction(accessor => accessor.get(IConfigurationService));
        const fileService = this.instantiationService.invokeFunction(accessor => accessor.get(IFileService));
        const workspaceService = this.instantiationService.invokeFunction(accessor => accessor.get(IWorkspaceContextService));

        if (!configurationService || !fileService || !workspaceService) {
            this.logService.error('listApplications - 无法获取必要的服务');
            return [];
        }

        // 使用路径解析器获取app_menu.py路径
        const pathResolver = new GATPathResolver(configurationService, workspaceService, fileService, this.logService);
        const appMenuPath = await pathResolver.getAppMenuPath();
        if (!appMenuPath) {
            this.logService.error('listApplications - 无法找到 app_menu.py 文件');
            return [];
        }
        this.logService.info('listApplications - 查找app_menu.py:', appMenuPath.toString());

        try {
            if (!(await fileService.exists(appMenuPath))) {
                this.logService.error('listApplications - 未找到app_menu.py:', appMenuPath.toString());
                return [];
            }

            const content = await fileService.readFile(appMenuPath);
            const allApps = this.parseAppMenu(content.value.toString());

            this.logService.info(`listApplications - 从app_menu.py解析出${allApps.length}个应用`);

            // 标记应该选中的应用
            if (appIdentifiers.length > 0) {
                const result = allApps.map(app => {
                    // 应用标识符匹配应用的value（通常是exec的basename）
                    const selected = appIdentifiers.some(id =>
                        id === app.value ||
                        // 也检查完整路径匹配
                        app.label.includes(id)
                    );

                    return {
                        ...app,
                        selected
                    };
                });

                // 记录哪些应用被标记为选中
                const selectedApps = result.filter(app => app.selected).map(app => app.label);
                this.logService.info(`listApplications - 标记${selectedApps.length}个应用为选中状态:`,
                    selectedApps.length > 0 ? JSON.stringify(selectedApps) : "无");

                return result;
            }

            return allApps;
        } catch (error) {
            this.logService.error('listApplications - 加载app_menu.py失败:', error);
            return [];
        }
    }

    /**
     * 解析app_menu.py文件中的应用类
     * @param content app_menu.py文件内容
     */
    private parseAppMenu(content: string): Array<{ label: string; value: string }> {
        const result: Array<{ label: string; value: string }> = [];

        try {
            // 使用正则表达式匹配所有@dataclass(frozen=True)类定义
            const classRegex = /@dataclass\(frozen=True\)[\s\n]*class\s+(\w+):/g;
            let match;

            while ((match = classRegex.exec(content)) !== null) {
                const className = match[1];

                // 获取类的属性范围
                const classStart = match.index;
                const nextClassMatch = content.indexOf('@dataclass(frozen=True)', classStart + 1);
                const classEnd = nextClassMatch !== -1 ? nextClassMatch : content.length;

                const classContent = content.substring(classStart, classEnd);

                // 解析类的属性
                const zhMatch = /zh:\s*str\s*=\s*'([^']*)'/.exec(classContent);
                const execMatch = /exec:\s*str\s*=\s*'([^']*)'/.exec(classContent);

                const label = zhMatch ? zhMatch[1] : className;
                let value = execMatch ? execMatch[1] : className;

                // 如果exec值包含路径，只提取basename部分
                if (value.includes('/')) {
                    value = value.split('/').pop() || value;
                    this.logService.debug(`提取exec路径的basename: ${value}`);
                }

                result.push({
                    label: label,
                    value: value
                });

                this.logService.debug(`解析到应用类: ${className}, label=${label}, value=${value}`);
            }
        } catch (error) {
            this.logService.error('解析app_menu.py时出错:', error);
        }

        return result;
    }

    /**
     * 清理资源
     */
    public dispose(): void {
        this._disposables.dispose();
        if (this.insertMethodWindow) {
            this.insertMethodWindow.dispose();
            this.insertMethodWindow = null;
        }
    }
}
