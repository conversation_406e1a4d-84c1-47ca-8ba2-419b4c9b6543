/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2023-2024 Your Name. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { localize2 } from '../../../../../nls.js';
import { ViewPane } from '../../../../browser/parts/views/viewPane.js';
import { IViewletViewOptions } from '../../../../browser/parts/views/viewsViewlet.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { IContextMenuService } from '../../../../../platform/contextview/browser/contextView.js';
import { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';
import { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';
import { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';
import { IOpenerService } from '../../../../../platform/opener/common/opener.js';
import { IThemeService } from '../../../../../platform/theme/common/themeService.js';
import { IStorageService } from '../../../../../platform/storage/common/storage.js';
import * as DOM from '../../../../../base/browser/dom.js';
import { IHoverService } from '../../../../../platform/hover/browser/hover.js';
import { IViewDescriptorService } from '../../../../common/views.js';
import { IAccessibleViewInformationService } from '../../../../services/accessibility/common/accessibleViewInformationService.js';
import { DisposableStore } from '../../../../../base/common/lifecycle.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { basename } from '../../../../../base/common/path.js';
import { joinPath } from '../../../../../base/common/resources.js';
import { GATPathResolver } from '../common/pathResolver.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';

export class CommonMethodView extends ViewPane {
	static readonly ID = 'workbench.views.gat.commonMethod';
	static readonly TITLE = localize2('commonMethod', "公共方法");

	private readonly disposables = new DisposableStore();
	private yamlMethodContainer!: HTMLElement;
	private yamlListContainer!: HTMLElement;
	private fileList: Map<string, HTMLElement> = new Map();
	private methodContainers: Map<string, Map<string, HTMLElement>> = new Map();
	private readonly pathResolver: GATPathResolver;

	private switchModeButton!: HTMLInputElement;
	private defaultShowMode: 'overview' | 'method' = 'overview';

	constructor(
		options: IViewletViewOptions,
		@IKeybindingService keybindingService: IKeybindingService,
		@IContextMenuService contextMenuService: IContextMenuService,
		@IConfigurationService configurationService: IConfigurationService,
		@IContextKeyService contextKeyService: IContextKeyService,
		@IViewDescriptorService viewDescriptorService: IViewDescriptorService,
		@IInstantiationService instantiationService: IInstantiationService,
		@IOpenerService openerService: IOpenerService,
		@IThemeService themeService: IThemeService,
		@IHoverService protected override readonly hoverService: IHoverService,
		@IStorageService storageService: IStorageService,
		@IFileService private readonly fileService: IFileService,
		@ILogService private readonly logService: ILogService,
		@IWorkspaceContextService private readonly workspaceContextService: IWorkspaceContextService,
		@IAccessibleViewInformationService accessibleViewInformationService?: IAccessibleViewInformationService
	) {
		super(options, keybindingService, contextMenuService, configurationService, contextKeyService, viewDescriptorService, instantiationService, openerService, themeService, hoverService, accessibleViewInformationService);

		// 初始化路径解析器
		this.pathResolver = new GATPathResolver(
			this.configurationService,
			this.workspaceContextService,
			this.fileService,
			this.logService
		);

		// 监听配置变更，当 gat.testcasePath 发生变化时刷新公共方法列表
		this._register(this.configurationService.onDidChangeConfiguration(e => {
			if (e.affectsConfiguration('gat.testcasePath')) {
				this.logService.info('检测到 gat.testcasePath 配置变更，刷新公共方法视图');
				this.refreshMethodList();
			}
		}));
	}

	protected override renderBody(container: HTMLElement): void {
		super.renderBody(container);

		this.yamlMethodContainer = DOM.append(container, DOM.$('.common-method-container')) as HTMLInputElement;
		// 添加CSS样式
		this.addStyles();
		//添加一个搜索框
		const searchBox = DOM.append(this.yamlMethodContainer, DOM.$('input.search-box'));

		//设置searchbox的长度适配容器宽度
		//searchBox.style.width = '100%'
		//设置搜索框的样式为addStyles中定义的样式
		searchBox.setAttribute('style', 'height: 30px;width: 90%;padding: 0 10px;border-radius: 4px;background-color: var(--vscode-sideBar-background);border: 1px solid var(--vscode-widget-border);');
		searchBox.setAttribute('placeholder', '搜索公共方法');
		searchBox.addEventListener('input', () => {
			//等待输入完成后
			setTimeout(() => {
				//刷新公共方法列表，显示匹配的公共方法
				this.searchMethodList((searchBox as HTMLInputElement).value);
			}, 500);
		});

		// 添加一个切换按钮
		const buttonContainer = DOM.append(this.yamlMethodContainer, DOM.$('div.button-container'));
		this.switchModeButton = DOM.append(buttonContainer, DOM.$('button.switch-mode-button'));
		this.updateSwitchModeButtonText();
		this.switchModeButton.addEventListener('click', () => {
			this.toggleShowMode();
		});

		// 添加YAML文件列表容器
		this.yamlListContainer = DOM.append(this.yamlMethodContainer, DOM.$('div.yaml-file-list'));

		// 加载YAML文件
		this.loadYamlFiles();
	}

	private addStyles(): void {
		// 添加CSS样式到head
		const styleSheet = document.createElement('style');
		styleSheet.textContent = `
			.common-method-container {
				height: 100%;
				overflow: auto;
			}
			.button-container {
				display: flex;
				justify-content: flex-end;
				height: 20px;
				margin-bottom: 5px;
				margin-top: 5px;
			}
			.switch-mode-button {
				background-color: var(--vscode-button-background);
				color: var(--vscode-button-foreground);
				border: 1px solid var(--vscode-button-border);
				border-radius: 2px;
				cursor: pointer;
				font-size: 12px;
			}

			.yaml-file-list {
				padding: 8px;
			}
			.yaml-file-item {
				margin-bottom: 10px;
				border: 1px solid var(--vscode-widget-border);
				border-radius: 4px;
			}
			.yaml-file-title {
				padding: 6px 8px;
				font-weight: bold;
				background-color: var(--vscode-sideBar-background);
				cursor: pointer;
				position: relative;
			}
			.yaml-file-title:hover {
				background-color: var(--vscode-list-hoverBackground);
			}
			.yaml-file-title::after {
				content: '▼';
				position: absolute;
				right: 10px;
			}
			.yaml-file-title.collapsed::after {
				content: '►';
			}
			.yaml-methods-list {
				padding: 8px;
			}
			.yaml-method-item {
				padding: 4px 8px;
				margin-bottom: 2px;
				cursor: pointer;
				border-radius: 3px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.yaml-method-item:hover {
				background-color: var(--vscode-list-hoverBackground);
			}
			.yaml-method-tooltip {
				position: fixed;
				z-index: 1000;
				background-color: var(--vscode-editorWidget-background, #252526);
				color: var(--vscode-editorWidget-foreground, #cccccc);
				border: 1px solid var(--vscode-focusBorder, #007fd4);
				padding: 12px;
				border-radius: 6px;
				box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
				max-width: 450px;
				max-height: 350px;
				overflow: auto;
				display: none;
				font-size: 13px;
				line-height: 1.5;
			}
			.yaml-method-tooltip h3 {
				margin: 0 0 12px 0;
				font-size: 16px;
				color: var(--vscode-textLink-foreground, #3794ff);
				padding-bottom: 8px;
				border-bottom: 1px solid var(--vscode-editorWidget-border, #454545);
			}
			.yaml-method-tooltip strong {
				color: var(--vscode-editorWidget-foreground, #cccccc);
				font-weight: 600;
			}
			.yaml-method-tooltip ul {
				margin: 8px 0;
				padding-left: 20px;
			}
			.yaml-method-tooltip li {
				margin-bottom: 8px;
			}
			.yaml-method-tooltip div {
				margin-bottom: 8px;
			}
			.yaml-method-tooltip pre {
				background-color: var(--vscode-textCodeBlock-background, #1e1e1e);
				color: var(--vscode-textCodeBlock-foreground, #d7d7d7);
				padding: 10px;
				border-radius: 5px;
				overflow: auto;
				margin: 8px 0;
				font-family: var(--vscode-editor-font-family, "Consolas, 'Courier New', monospace");
				border: 1px solid var(--vscode-editorWidget-border, #454545);
			}
			.yaml-method-section {
				margin: 12px 0;
				padding-top: 10px;
				border-top: 1px dashed var(--vscode-editorWidget-border, #454545);
			}
			.yaml-error {
				color: var(--vscode-errorForeground);
				padding: 8px;
			}
		`;
		document.head.appendChild(styleSheet);
		this.disposables.add({ dispose: () => styleSheet.remove() });
	}

	// 添加一个简单的YAML解析函数
	private parseYaml(content: string): any {
		try {
			const ymlObject = (window as any).jsyaml.load(content);
			return ymlObject;
		} catch (error) {
			this.logService.error('YAML解析错误:', error);
			return {};
		}
	}

	private async loadYamlFiles(): Promise<void> {
		try {
			// 使用路径解析器获取 action_keywords 目录
			const actionKeywordsPath = await this.pathResolver.getActionKeywordsPath();
			if (!actionKeywordsPath) {
				this.logService.error('无法获取有效的 action_keywords 路径');
				this.showErrorMessage('未找到公共方法定义目录，请检查配置');
				return;
			}

			// 获取 action_yaml 子目录
			const actionYamlPath = joinPath(actionKeywordsPath, 'action_yaml');
			try {
				await this.fileService.resolve(actionYamlPath);
				this.logService.info(`找到 action_yaml 目录: ${actionYamlPath.toString()}`);
			} catch (error) {
				this.logService.error('action_yaml 子目录不存在');
				this.showErrorMessage('未找到公共方法定义目录，请检查配置');
				return;
			}


			// 获取文件系统条目
			const dirContent = await this.fileService.resolve(actionYamlPath);
			if (!dirContent.children) {
				this.logService.error('无法读取action_yaml目录内容');
				return;
			}

			// 过滤出YAML文件
			const yamlFileEntries = dirContent.children.filter(entry =>
				entry.name.endsWith('.yml') || entry.name.endsWith('.yaml')
			);

			// 遍历YAML文件
			for (const entry of yamlFileEntries) {
				const fileUri = entry.resource;
				const fileName = basename(fileUri.path);

				try {
					// 读取文件内容
					const content = await this.fileService.readFile(fileUri);
					const yamlContent = content.value.toString();

					// 使用自定义函数解析YAML内容
					const methodsData = this.parseYaml(yamlContent);

					// 创建文件项
					await this.createFileItem(fileName, methodsData);
				} catch (error) {
					this.logService.error(`读取YAML文件 ${fileName} 失败:`, error);
				}
			}
		} catch (error) {
			this.logService.error('加载YAML文件时出错:', error);

			// 添加错误提示
			const errorElement = DOM.append(this.yamlListContainer, DOM.$('div.yaml-error'));
			errorElement.textContent = `加载YAML文件时出错: ${error}`;
		}
	}

	private async createFileItem(fileName: string, methodsData: any): Promise<void> {
		// 创建文件项容器
		const fileItem = DOM.append(this.yamlListContainer, DOM.$('div.yaml-file-item'));

		// 创建文件标题
		const fileSummary = DOM.append(fileItem, DOM.$('div.yaml-file-title'));
		fileSummary.textContent = fileName;
		fileSummary.title = `${fileName} - YAML方法文件`;

		// 创建方法列表容器
		const methodsList = DOM.append(fileItem, DOM.$('div.yaml-methods-list'));
		this.fileList.set(fileName, methodsList);
		this.methodContainers.set(fileName, new Map());

		// 添加点击展开/折叠功能
		fileSummary.addEventListener('click', () => {
			if (methodsList.style.display === 'none') {
				methodsList.style.display = 'block';
				fileSummary.classList.remove('collapsed');
			} else {
				methodsList.style.display = 'none';
				fileSummary.classList.add('collapsed');
			}
		});

		// 默认展开
		methodsList.style.display = 'block';

		// 添加方法项
		for (const [methodName, methodData] of Object.entries(methodsData)) {
			await this.createMethodItem(fileName, methodName, methodData);
		}
	}

	private async createMethodItem(fileName: string, methodName: string, methodData: any): Promise<void> {
		if (!this.fileList.has(fileName)) {
			return;
		}

		const methodsList = this.fileList.get(fileName)!;
		const methodsMap = this.methodContainers.get(fileName)!;

		// 创建方法项
		const methodItem = DOM.append(methodsList, DOM.$('div.yaml-method-item', {
			'data-method-name': methodName, 'data-method-overview': methodData.overview
		}));
		//methodItem.textContent = methodName;
		//修改当前展示的方法名称为方法概述
		if (this.defaultShowMode === 'overview') {
			methodItem.textContent = methodData.overview;
		} else {
			methodItem.textContent = methodName;
		}
		methodItem.title = methodData.overview || methodName;


		// 保存方法项引用
		methodsMap.set(methodName, methodItem);

		// 创建悬停文本内容
		const tooltipContent = document.createElement('div');
		tooltipContent.className = 'yaml-method-tooltip';
		document.body.appendChild(tooltipContent);

		//设置当前methodItem节点可以拖拽
		methodItem.draggable = true;
		methodItem.addEventListener('dragstart', (e) => {
			if (e.dataTransfer) {
				//如果tooltipContent悬停文本内容存在，关闭tooltipContent
				if (tooltipContent.style.display === 'block') {
					tooltipContent.style.display = 'none';
				}
				//设置拖拽数据
				e.dataTransfer.setData('text/plain', JSON.stringify({ name: methodName, overview: methodData.overview }));
			} else {
				this.logService.error(`监听拖拽失败: ${e}`);
			}
		});

		// 添加悬停内容
		const tooltipTitle = document.createElement('h3');
		tooltipTitle.textContent = methodName;
		tooltipContent.appendChild(tooltipTitle);

		// 添加方法概述
		if (methodData.overview) {
			const overviewSection = document.createElement('div');
			const overviewTitle = document.createElement('strong');
			overviewTitle.textContent = '概述: ';
			overviewSection.appendChild(overviewTitle);
			overviewSection.appendChild(document.createTextNode(methodData.overview));
			tooltipContent.appendChild(overviewSection);
		}

		// 添加方法描述
		if (methodData.description) {
			const descriptionSection = document.createElement('div');
			descriptionSection.classList.add('yaml-method-section');
			const descTitle = document.createElement('strong');
			descTitle.textContent = '描述: ';
			descriptionSection.appendChild(descTitle);
			descriptionSection.appendChild(document.createTextNode(methodData.description));
			tooltipContent.appendChild(descriptionSection);
		}

		// 添加方法分类
		if (methodData.category) {
			const categorySection = document.createElement('div');
			const catTitle = document.createElement('strong');
			catTitle.textContent = '分类: ';
			categorySection.appendChild(catTitle);
			categorySection.appendChild(document.createTextNode(methodData.category));
			tooltipContent.appendChild(categorySection);
		}

		// 添加方法参数
		if (methodData.parameters && methodData.parameters.length > 0) {
			const paramsSection = document.createElement('div');
			paramsSection.classList.add('yaml-method-section');

			const paramsTitle = document.createElement('strong');
			paramsTitle.textContent = '参数:';
			paramsSection.appendChild(paramsTitle);
			tooltipContent.appendChild(paramsSection);

			const paramsList = document.createElement('ul');
			for (const param of methodData.parameters) {
				const paramItem = document.createElement('li');
				const paramName = document.createElement('strong');
				paramName.textContent = `${param.paramname} (${param.name}): `;
				paramItem.appendChild(paramName);
				paramItem.appendChild(document.createTextNode(param.description));

				if (param.type) {
					const paramType = document.createElement('div');
					paramType.textContent = `类型: ${param.type}`;
					paramType.style.marginLeft = '20px';
					paramType.style.color = 'var(--vscode-textLink-activeForeground, #89d1ff)';
					paramItem.appendChild(paramType);
				}

				paramsList.appendChild(paramItem);
			}
			tooltipContent.appendChild(paramsList);
		}

		// 添加方法示例
		if (methodData.examples) {
			const examplesSection = document.createElement('div');
			examplesSection.classList.add('yaml-method-section');

			const examplesTitle = document.createElement('strong');
			examplesTitle.textContent = '示例:';
			examplesSection.appendChild(examplesTitle);
			tooltipContent.appendChild(examplesSection);

			const examplesCode = document.createElement('pre');
			examplesCode.textContent = methodData.examples;
			tooltipContent.appendChild(examplesCode);
		}

		// 添加鼠标悬停显示提示
		methodItem.addEventListener('mouseenter', (e) => {
			// 获取方法项的位置信息
			const rect = methodItem.getBoundingClientRect();
			const windowWidth = window.innerWidth;

			// 计算提示框最佳位置，避免超出屏幕
			const tooltipWidth = 450; // 最大宽度
			let leftPos = rect.right - 10;

			// 如果提示框会超出屏幕右侧，则显示在左侧
			if (leftPos + tooltipWidth > windowWidth) {
				leftPos = Math.max(10, rect.left - tooltipWidth - 10);
			}

			// 设置提示框位置，确保在可视区域内
			tooltipContent.style.left = `${leftPos}px`;
			tooltipContent.style.top = `${Math.max(10, rect.top - 30)}px`;

			// 显示提示框
			tooltipContent.style.display = 'block';
		});

		// 鼠标离开隐藏提示
		methodItem.addEventListener('mouseleave', () => {
			// 延迟隐藏，给用户时间移动到tooltip上
			setTimeout(() => {
				if (!tooltipContent.matches(':hover')) {
					tooltipContent.style.display = 'none';
				}
			}, 100);
		});

		// 为tooltip添加鼠标事件，保持显示
		tooltipContent.addEventListener('mouseenter', () => {
			tooltipContent.style.display = 'block';
		});

		tooltipContent.addEventListener('mouseleave', () => {
			tooltipContent.style.display = 'none';
		});

		// 添加点击复制方法名功能
		methodItem.addEventListener('click', () => {
			// 使用剪贴板API复制方法名
			navigator.clipboard.writeText(methodName).catch(error => {
				this.logService.error(`复制方法名到剪贴板失败: ${error}`);
			});
		});


		// 注册销毁函数
		this.disposables.add({
			dispose: () => {
				tooltipContent.remove();
			}
		});
	}

	protected override layoutBody(height: number, width: number): void {
		super.layoutBody(height, width);
	}

	override dispose(): void {
		this.disposables.dispose();
		super.dispose();
	}

	/**
	 * 显示错误信息
	 */
	private showErrorMessage(message: string): void {
		// 清空YAML方法容器
		DOM.clearNode(this.yamlMethodContainer);

		// 创建错误消息元素
		const errorElement = DOM.append(this.yamlMethodContainer, DOM.$('.yaml-error-message'));
		errorElement.textContent = message;

		// 记录错误
		this.logService.error(message);
	}

	/**
	 * 刷新公共方法列表，清空当前内容后重新加载
	 */
	private async refreshMethodList(): Promise<void> {
		// 如果容器尚未渲染，直接返回
		if (!this.yamlMethodContainer) {
			return;
		}

		// 清空顶层容器下所有内容（包括错误信息或现有列表）
		DOM.clearNode(this.yamlMethodContainer);

		// 重新创建 YAML 文件列表容器
		this.yamlListContainer = DOM.append(this.yamlMethodContainer, DOM.$('div.yaml-file-list'));

		// 重置缓存
		this.fileList.clear();
		this.methodContainers.clear();

		// 重新加载 YAML 文件
		await this.loadYamlFiles();
	}

	//刷新公共方法列表，显示匹配的公共方法
	private async searchMethodList(keyword: string): Promise<void> {
		//console.log('searchMethodList:', keyword);
		// 遍历文件列表，查找匹配的公共方法
		for (const [fileName] of this.fileList.entries()) {
			// 遍历方法列表，查找匹配的公共方法
			let isFileFind = false;
			for (const [methodName, methodItem] of this.methodContainers.get(fileName)!.entries()) {
				this.logService.info(`找到 action_yaml 目录: ${methodName.toString()}`);
				//获取methodNode的概述
				const methodOverview = methodItem.title;
				if (methodOverview.toLowerCase().includes(keyword.toLowerCase()) || methodName.toString().toLowerCase().includes(keyword.toLowerCase())) {
					methodItem.style.display = 'block';
					isFileFind = true;
				} else {
					methodItem.style.display = 'none';
				}
			}

			const methodsList = this.fileList.get(fileName)!;
			if (methodsList) {
				const parent: HTMLElement | null = methodsList.closest('.yaml-file-item');
				if (parent) {
					parent.style.display = isFileFind ? 'block' : 'none';
				}
			}
		}
	}

	private updateSwitchModeButtonText(): void {
		this.switchModeButton.textContent = this.defaultShowMode === 'overview' ? '展示方法名' : '展示方法概述';
	}

	private async toggleShowMode(): Promise<void> {
		this.defaultShowMode = this.defaultShowMode === 'overview' ? 'method' : 'overview';
		this.updateSwitchModeButtonText();

		for (const [fileName] of this.fileList.entries()) {
			// 遍历方法列表，查找匹配的公共方法
			for (const [methodName, methodItem] of this.methodContainers.get(fileName)!.entries()) {
				//获取methodNode的概述
				if (this.defaultShowMode === 'overview') {
					methodItem.textContent = methodItem.getAttribute('data-method-overview') || methodName;
				} else {
					methodItem.textContent = methodName;
				}
			}
		}
	}

}
